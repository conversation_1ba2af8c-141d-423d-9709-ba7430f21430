const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class Database {
  constructor() {
    // Use absolute path to ensure it works regardless of working directory
    const defaultPath = path.resolve(__dirname, '../../database/al_fayasel.db');
    this.dbPath = process.env.DB_PATH ? path.resolve(process.env.DB_PATH) : defaultPath;
    console.log('Database path:', this.dbPath);
    this.ensureDirectoryExists();
    this.db = null;
  }

  ensureDirectoryExists() {
    const dir = path.dirname(this.dbPath);
    console.log('Database directory:', dir);
    console.log('Directory exists:', fs.existsSync(dir));
    if (!fs.existsSync(dir)) {
      console.log('Creating directory:', dir);
      fs.mkdirSync(dir, { recursive: true });
    }
  }

  async connect() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('Error opening database:', err);
          reject(err);
        } else {
          console.log('Connected to SQLite database');
          resolve();
        }
      });
    });
  }

  async close() {
    return new Promise((resolve, reject) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            reject(err);
          } else {
            console.log('Database connection closed');
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }

  async run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  async get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  async all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  async initializeTables() {
    const tables = [
      // Admin users table
      `CREATE TABLE IF NOT EXISTS admin_users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        email TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // About us content
      `CREATE TABLE IF NOT EXISTS about_content (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        section TEXT UNIQUE NOT NULL,
        title TEXT,
        content TEXT,
        image_url TEXT,
        order_index INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Services
      `CREATE TABLE IF NOT EXISTS services (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        icon TEXT,
        image_url TEXT,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Suppliers
      `CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        logo_url TEXT,
        website TEXT,
        contact_email TEXT,
        contact_phone TEXT,
        country TEXT,
        is_active BOOLEAN DEFAULT 1,
        order_index INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Product categories
      `CREATE TABLE IF NOT EXISTS product_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        order_index INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Products
      `CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        category_id INTEGER,
        supplier_id INTEGER,
        image_url TEXT,
        price DECIMAL(10,2),
        sku TEXT,
        is_active BOOLEAN DEFAULT 1,
        order_index INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES product_categories(id),
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
      )`,

      // Corporate clients
      `CREATE TABLE IF NOT EXISTS corporate_clients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        logo_url TEXT,
        website TEXT,
        description TEXT,
        partnership_year INTEGER,
        is_featured BOOLEAN DEFAULT 0,
        order_index INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // News and updates
      `CREATE TABLE IF NOT EXISTS news (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        content TEXT,
        excerpt TEXT,
        image_url TEXT,
        author TEXT,
        is_published BOOLEAN DEFAULT 1,
        published_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Contact information
      `CREATE TABLE IF NOT EXISTS contact_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        field_name TEXT UNIQUE NOT NULL,
        field_value TEXT,
        field_type TEXT DEFAULT 'text',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Team members
      `CREATE TABLE IF NOT EXISTS team_members (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        position TEXT,
        bio TEXT,
        photo_url TEXT,
        email TEXT,
        linkedin_url TEXT,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Site settings
      `CREATE TABLE IF NOT EXISTS site_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type TEXT DEFAULT 'text',
        description TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    try {
      for (const table of tables) {
        await this.run(table);
      }
      console.log('All tables created successfully');
      await this.seedInitialData();
    } catch (error) {
      console.error('Error creating tables:', error);
      throw error;
    }
  }

  async seedInitialData() {
    // Check if admin user exists
    const adminExists = await this.get('SELECT id FROM admin_users WHERE username = ?', ['admin']);
    
    if (!adminExists) {
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD || 'admin123', 10);
      
      await this.run(
        'INSERT INTO admin_users (username, password_hash, email) VALUES (?, ?, ?)',
        ['admin', hashedPassword, '<EMAIL>']
      );
      console.log('Default admin user created');
    }

    // Seed basic contact info
    const contactFields = [
      ['company_name', 'Al-Fayasel Drugstore'],
      ['phone', '******-567-8900'],
      ['email', '<EMAIL>'],
      ['address', '123 Pharmaceutical Street, Medical District, City, Country'],
      ['working_hours', 'Monday - Friday: 8:00 AM - 6:00 PM'],
      ['google_map_url', 'https://maps.google.com/embed?pb=!1m18!1m12!1m3!1d3024.*********!2d-74.0059413!3d40.7127753!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNDDCsDQyJzQ2LjAiTiA3NMKwMDAnMjEuNCJX!5e0!3m2!1sen!2sus!4v*********0123!5m2!1sen!2sus']
    ];

    for (const [key, value] of contactFields) {
      const exists = await this.get('SELECT id FROM contact_info WHERE field_name = ?', [key]);
      if (!exists) {
        await this.run(
          'INSERT INTO contact_info (field_name, field_value) VALUES (?, ?)',
          [key, value]
        );
      }
    }

    // Seed basic site settings
    const siteSettings = [
      ['site_title', 'Al-Fayasel Drugstore - Pharmaceutical Distribution Excellence'],
      ['site_description', 'Leading wholesale pharmaceutical distributor serving corporate clients with quality medicines and reliable service.'],
      ['hero_title', 'Pharmaceutical Distribution Excellence'],
      ['hero_subtitle', 'Your trusted partner in wholesale pharmaceutical distribution, serving corporate clients with quality and reliability.'],
      ['company_established', '2010'],
      ['total_clients', '150+'],
      ['total_suppliers', '50+']
    ];

    for (const [key, value] of siteSettings) {
      const exists = await this.get('SELECT id FROM site_settings WHERE setting_key = ?', [key]);
      if (!exists) {
        await this.run(
          'INSERT INTO site_settings (setting_key, setting_value) VALUES (?, ?)',
          [key, value]
        );
      }
    }

    console.log('Initial data seeded successfully');
  }
}

module.exports = Database;
