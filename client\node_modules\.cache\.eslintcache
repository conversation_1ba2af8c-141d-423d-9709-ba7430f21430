[{"C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\Products.js": "3", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\About.js": "4", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\Home.js": "5", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\Suppliers.js": "6", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\SupplierDetail.js": "7", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\Services.js": "8", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\News.js": "9", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\Contact.js": "10", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\Clients.js": "11", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\NewsDetail.js": "12", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\components\\Layout.js": "13", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminServices.js": "14", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminAbout.js": "15", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminDashboard.js": "16", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminLayout.js": "17", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminSuppliers.js": "18", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminLogin.js": "19", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminNews.js": "20", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminTeam.js": "21", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminContact.js": "22", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminProducts.js": "23", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminSettings.js": "24", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminClients.js": "25", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\contexts\\SiteDataContext.js": "26", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\contexts\\AuthContext.js": "27", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\components\\ErrorMessage.js": "28", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\components\\LoadingSpinner.js": "29", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\components\\SuccessMessage.js": "30", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\components\\Footer.js": "31", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\components\\Header.js": "32", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\services\\api.js": "33", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\components\\Modal.js": "34", "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\components\\CrudTable.js": "35"}, {"size": 354, "mtime": 1754992666438, "results": "36", "hashOfConfig": "37"}, {"size": 3515, "mtime": 1754992750555, "results": "38", "hashOfConfig": "37"}, {"size": 8211, "mtime": 1754993051176, "results": "39", "hashOfConfig": "37"}, {"size": 7205, "mtime": 1754992944280, "results": "40", "hashOfConfig": "37"}, {"size": 13188, "mtime": 1754992914585, "results": "41", "hashOfConfig": "37"}, {"size": 5685, "mtime": 1754993076204, "results": "42", "hashOfConfig": "37"}, {"size": 5693, "mtime": 1754993100447, "results": "43", "hashOfConfig": "37"}, {"size": 5617, "mtime": 1754992979079, "results": "44", "hashOfConfig": "37"}, {"size": 6469, "mtime": 1754993144863, "results": "45", "hashOfConfig": "37"}, {"size": 9802, "mtime": 1754993019920, "results": "46", "hashOfConfig": "37"}, {"size": 4112, "mtime": 1754993120655, "results": "47", "hashOfConfig": "37"}, {"size": 3823, "mtime": 1754993168397, "results": "48", "hashOfConfig": "37"}, {"size": 358, "mtime": 1754992779091, "results": "49", "hashOfConfig": "37"}, {"size": 7831, "mtime": 1754993412225, "results": "50", "hashOfConfig": "37"}, {"size": 7713, "mtime": 1754993855215, "results": "51", "hashOfConfig": "37"}, {"size": 8628, "mtime": 1754993265677, "results": "52", "hashOfConfig": "37"}, {"size": 6817, "mtime": 1754993230762, "results": "53", "hashOfConfig": "37"}, {"size": 1869, "mtime": 1754993424398, "results": "54", "hashOfConfig": "37"}, {"size": 5828, "mtime": 1754993843592, "results": "55", "hashOfConfig": "37"}, {"size": 1826, "mtime": 1754993445284, "results": "56", "hashOfConfig": "37"}, {"size": 1774, "mtime": 1754993455004, "results": "57", "hashOfConfig": "37"}, {"size": 5084, "mtime": 1754993474974, "results": "58", "hashOfConfig": "37"}, {"size": 11269, "mtime": 1754993353252, "results": "59", "hashOfConfig": "37"}, {"size": 4942, "mtime": 1754993494655, "results": "60", "hashOfConfig": "37"}, {"size": 1807, "mtime": 1754993435267, "results": "61", "hashOfConfig": "37"}, {"size": 1277, "mtime": 1754992771658, "results": "62", "hashOfConfig": "37"}, {"size": 2520, "mtime": 1754992763693, "results": "63", "hashOfConfig": "37"}, {"size": 801, "mtime": 1754992853317, "results": "64", "hashOfConfig": "37"}, {"size": 458, "mtime": 1754992845495, "results": "65", "hashOfConfig": "37"}, {"size": 801, "mtime": 1754992861279, "results": "66", "hashOfConfig": "37"}, {"size": 6361, "mtime": 1754992836227, "results": "67", "hashOfConfig": "37"}, {"size": 9033, "mtime": 1754992810437, "results": "68", "hashOfConfig": "37"}, {"size": 5496, "mtime": 1754992731685, "results": "69", "hashOfConfig": "37"}, {"size": 2203, "mtime": 1754993314990, "results": "70", "hashOfConfig": "37"}, {"size": 7570, "mtime": 1754993302465, "results": "71", "hashOfConfig": "37"}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "54ccpr", {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\Products.js", ["177", "178"], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\Home.js", ["179"], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\Suppliers.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\SupplierDetail.js", ["180", "181"], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\Services.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\News.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\Contact.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\Clients.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\pages\\NewsDetail.js", ["182"], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\components\\Layout.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminServices.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminAbout.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminDashboard.js", ["183"], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminLayout.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminSuppliers.js", ["184", "185"], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminLogin.js", ["186"], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminNews.js", ["187"], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminTeam.js", ["188"], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminContact.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminProducts.js", ["189", "190", "191", "192", "193"], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminSettings.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\AdminClients.js", ["194"], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\contexts\\SiteDataContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\contexts\\AuthContext.js", ["195"], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\components\\ErrorMessage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\components\\SuccessMessage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\components\\Modal.js", [], [], "C:\\Users\\<USER>\\Desktop\\Al-Faiasel\\client\\src\\admin\\components\\CrudTable.js", [], [], {"ruleId": "196", "severity": 1, "message": "197", "line": 4, "column": 20, "nodeType": "198", "messageId": "199", "endLine": 4, "endColumn": 28}, {"ruleId": "200", "severity": 1, "message": "201", "line": 27, "column": 6, "nodeType": "202", "endLine": 27, "endColumn": 15, "suggestions": "203"}, {"ruleId": "196", "severity": 1, "message": "204", "line": 35, "column": 21, "nodeType": "198", "messageId": "199", "endLine": 35, "endColumn": 28}, {"ruleId": "196", "severity": 1, "message": "205", "line": 5, "column": 54, "nodeType": "198", "messageId": "199", "endLine": 5, "endColumn": 61}, {"ruleId": "200", "severity": 1, "message": "206", "line": 18, "column": 6, "nodeType": "202", "endLine": 18, "endColumn": 10, "suggestions": "207"}, {"ruleId": "200", "severity": 1, "message": "208", "line": 18, "column": 6, "nodeType": "202", "endLine": 18, "endColumn": 10, "suggestions": "209"}, {"ruleId": "196", "severity": 1, "message": "210", "line": 12, "column": 3, "nodeType": "198", "messageId": "199", "endLine": 12, "endColumn": 14}, {"ruleId": "196", "severity": 1, "message": "211", "line": 4, "column": 8, "nodeType": "198", "messageId": "199", "endLine": 4, "endColumn": 13}, {"ruleId": "196", "severity": 1, "message": "212", "line": 5, "column": 8, "nodeType": "198", "messageId": "199", "endLine": 5, "endColumn": 22}, {"ruleId": "196", "severity": 1, "message": "213", "line": 1, "column": 27, "nodeType": "198", "messageId": "199", "endLine": 1, "endColumn": 36}, {"ruleId": "196", "severity": 1, "message": "212", "line": 4, "column": 8, "nodeType": "198", "messageId": "199", "endLine": 4, "endColumn": 22}, {"ruleId": "196", "severity": 1, "message": "212", "line": 4, "column": 8, "nodeType": "198", "messageId": "199", "endLine": 4, "endColumn": 22}, {"ruleId": "196", "severity": 1, "message": "214", "line": 2, "column": 10, "nodeType": "198", "messageId": "199", "endLine": 2, "endColumn": 16}, {"ruleId": "196", "severity": 1, "message": "215", "line": 3, "column": 10, "nodeType": "198", "messageId": "199", "endLine": 3, "endColumn": 16}, {"ruleId": "196", "severity": 1, "message": "216", "line": 3, "column": 18, "nodeType": "198", "messageId": "199", "endLine": 3, "endColumn": 24}, {"ruleId": "196", "severity": 1, "message": "217", "line": 3, "column": 26, "nodeType": "198", "messageId": "199", "endLine": 3, "endColumn": 33}, {"ruleId": "196", "severity": 1, "message": "218", "line": 3, "column": 35, "nodeType": "198", "messageId": "199", "endLine": 3, "endColumn": 42}, {"ruleId": "196", "severity": 1, "message": "212", "line": 4, "column": 8, "nodeType": "198", "messageId": "199", "endLine": 4, "endColumn": 22}, {"ruleId": "200", "severity": 1, "message": "219", "line": 22, "column": 6, "nodeType": "202", "endLine": 22, "endColumn": 8, "suggestions": "220"}, "no-unused-vars", "'FaFilter' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["221"], "'contact' is assigned a value but never used.", "'FaPhone' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSupplier'. Either include it or remove the dependency array.", ["222"], "React Hook useEffect has a missing dependency: 'fetchArticle'. Either include it or remove the dependency array.", ["223"], "'FaChartLine' is defined but never used.", "'Modal' is defined but never used.", "'LoadingSpinner' is defined but never used.", "'useEffect' is defined but never used.", "'motion' is defined but never used.", "'FaPlus' is defined but never used.", "'FaEdit' is defined but never used.", "'FaTrash' is defined but never used.", "'FaImage' is defined but never used.", "React Hook useEffect has a missing dependency: 'checkAuthStatus'. Either include it or remove the dependency array.", ["224"], {"desc": "225", "fix": "226"}, {"desc": "227", "fix": "228"}, {"desc": "229", "fix": "230"}, {"desc": "231", "fix": "232"}, "Update the dependencies array to be: [fetchProducts, filters]", {"range": "233", "text": "234"}, "Update the dependencies array to be: [fetchSupplier, id]", {"range": "235", "text": "236"}, "Update the dependencies array to be: [fetchArticle, id]", {"range": "237", "text": "238"}, "Update the dependencies array to be: [checkAuthStatus]", {"range": "239", "text": "240"}, [810, 819], "[fetchProducts, filters]", [676, 680], "[fetchSupplier, id]", [649, 653], "[fetchArticle, id]", [646, 648], "[checkAuthStatus]"]