{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\admin\\\\AdminSuppliers.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport apiService from '../services/api';\nimport CrudTable from './components/CrudTable';\nimport Modal from './components/Modal';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport SuccessMessage from '../components/SuccessMessage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminSuppliers = () => {\n  _s();\n  const [suppliers, setSuppliers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  useEffect(() => {\n    fetchSuppliers();\n  }, []);\n  const fetchSuppliers = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.admin.getSuppliers();\n      setSuppliers(response.data);\n    } catch (error) {\n      setError('Failed to load suppliers');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const columns = [{\n    key: 'logo_url',\n    label: 'Logo',\n    type: 'image'\n  }, {\n    key: 'name',\n    label: 'Supplier Name',\n    sortable: true\n  }, {\n    key: 'country',\n    label: 'Country',\n    sortable: true\n  }, {\n    key: 'contact_email',\n    label: 'Email'\n  }, {\n    key: 'is_active',\n    label: 'Status',\n    type: 'boolean'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-800 mb-2\",\n        children: \"Suppliers Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Manage your supplier network\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n      message: error,\n      onClose: () => setError(null),\n      className: \"mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(SuccessMessage, {\n      message: success,\n      onClose: () => setSuccess(null),\n      className: \"mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(CrudTable, {\n      title: \"Suppliers\",\n      data: suppliers,\n      columns: columns,\n      loading: loading,\n      addButtonText: \"Add Supplier\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminSuppliers, \"aXLuyHn/8SJYgvHbpPg+MGo5Efk=\");\n_c = AdminSuppliers;\nexport default AdminSuppliers;\nvar _c;\n$RefreshReg$(_c, \"AdminSuppliers\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "apiService", "CrudTable", "Modal", "LoadingSpinner", "ErrorMessage", "SuccessMessage", "jsxDEV", "_jsxDEV", "AdminSuppliers", "_s", "suppliers", "setSuppliers", "loading", "setLoading", "error", "setError", "success", "setSuccess", "fetchSuppliers", "response", "admin", "getSuppliers", "data", "columns", "key", "label", "type", "sortable", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "onClose", "title", "addButtonText", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/admin/AdminSuppliers.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport apiService from '../services/api';\nimport CrudTable from './components/CrudTable';\nimport Modal from './components/Modal';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport SuccessMessage from '../components/SuccessMessage';\n\nconst AdminSuppliers = () => {\n  const [suppliers, setSuppliers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  useEffect(() => {\n    fetchSuppliers();\n  }, []);\n\n  const fetchSuppliers = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.admin.getSuppliers();\n      setSuppliers(response.data);\n    } catch (error) {\n      setError('Failed to load suppliers');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const columns = [\n    { key: 'logo_url', label: 'Logo', type: 'image' },\n    { key: 'name', label: 'Supplier Name', sortable: true },\n    { key: 'country', label: 'Country', sortable: true },\n    { key: 'contact_email', label: 'Email' },\n    { key: 'is_active', label: 'Status', type: 'boolean' }\n  ];\n\n  return (\n    <div>\n      <div className=\"mb-6\">\n        <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">Suppliers Management</h1>\n        <p className=\"text-gray-600\">Manage your supplier network</p>\n      </div>\n\n      {error && <ErrorMessage message={error} onClose={() => setError(null)} className=\"mb-4\" />}\n      {success && <SuccessMessage message={success} onClose={() => setSuccess(null)} className=\"mb-4\" />}\n\n      <CrudTable\n        title=\"Suppliers\"\n        data={suppliers}\n        columns={columns}\n        loading={loading}\n        addButtonText=\"Add Supplier\"\n      />\n    </div>\n  );\n};\n\nexport default AdminSuppliers;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdmB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAMnB,UAAU,CAACoB,KAAK,CAACC,YAAY,CAAC,CAAC;MACtDV,YAAY,CAACQ,QAAQ,CAACG,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,QAAQ,CAAC,0BAA0B,CAAC;IACtC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,OAAO,GAAG,CACd;IAAEC,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAQ,CAAC,EACjD;IAAEF,GAAG,EAAE,MAAM;IAAEC,KAAK,EAAE,eAAe;IAAEE,QAAQ,EAAE;EAAK,CAAC,EACvD;IAAEH,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEE,QAAQ,EAAE;EAAK,CAAC,EACpD;IAAEH,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACxC;IAAED,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAU,CAAC,CACvD;EAED,oBACEnB,OAAA;IAAAqB,QAAA,gBACErB,OAAA;MAAKsB,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBrB,OAAA;QAAIsB,SAAS,EAAC,uCAAuC;QAAAD,QAAA,EAAC;MAAoB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/E1B,OAAA;QAAGsB,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAC;MAA4B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,EAELnB,KAAK,iBAAIP,OAAA,CAACH,YAAY;MAAC8B,OAAO,EAAEpB,KAAM;MAACqB,OAAO,EAAEA,CAAA,KAAMpB,QAAQ,CAAC,IAAI,CAAE;MAACc,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACzFjB,OAAO,iBAAIT,OAAA,CAACF,cAAc;MAAC6B,OAAO,EAAElB,OAAQ;MAACmB,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,IAAI,CAAE;MAACY,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAElG1B,OAAA,CAACN,SAAS;MACRmC,KAAK,EAAC,WAAW;MACjBd,IAAI,EAAEZ,SAAU;MAChBa,OAAO,EAAEA,OAAQ;MACjBX,OAAO,EAAEA,OAAQ;MACjByB,aAAa,EAAC;IAAc;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxB,EAAA,CAjDID,cAAc;AAAA8B,EAAA,GAAd9B,cAAc;AAmDpB,eAAeA,cAAc;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}