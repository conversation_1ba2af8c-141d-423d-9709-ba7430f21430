{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n/**\n * @typedef {Object} StateDefinitions\n * @property {{[event: string]: { target: string; actions?: Array<string> }}} [on]\n */\n\n/**\n * @typedef {Object} Options\n * @property {{[state: string]: StateDefinitions}} states\n * @property {object} context;\n * @property {string} initial\n */\n\n/**\n * @typedef {Object} Implementation\n * @property {{[actionName: string]: (ctx: object, event: any) => object}} actions\n */\n\n/**\n * A simplified `createMachine` from `@xstate/fsm` with the following differences:\n *\n *  - the returned machine is technically a \"service\". No `interpret(machine).start()` is needed.\n *  - the state definition only support `on` and target must be declared with { target: 'nextState', actions: [] } explicitly.\n *  - event passed to `send` must be an object with `type` property.\n *  - actions implementation will be [assign action](https://xstate.js.org/docs/guides/context.html#assign-action) if you return any value.\n *  Do not return anything if you just want to invoke side effect.\n *\n * The goal of this custom function is to avoid installing the entire `'xstate/fsm'` package, while enabling modeling using\n * state machine. You can copy the first parameter into the editor at https://stately.ai/viz to visualize the state machine.\n *\n * @param {Options} options\n * @param {Implementation} implementation\n */\nfunction createMachine(_ref, _ref2) {\n  var states = _ref.states,\n    context = _ref.context,\n    initial = _ref.initial;\n  var actions = _ref2.actions;\n  var currentState = initial;\n  var currentContext = context;\n  return {\n    send: function send(event) {\n      var currentStateOn = states[currentState].on;\n      var transitionConfig = currentStateOn && currentStateOn[event.type];\n      if (transitionConfig) {\n        currentState = transitionConfig.target;\n        if (transitionConfig.actions) {\n          transitionConfig.actions.forEach(function (actName) {\n            var actionImpl = actions[actName];\n            var nextContextValue = actionImpl && actionImpl(currentContext, event);\n            if (nextContextValue) {\n              currentContext = _objectSpread(_objectSpread({}, currentContext), nextContextValue);\n            }\n          });\n        }\n      }\n    }\n  };\n}\nexport default createMachine;", "map": {"version": 3, "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "arg", "_toPrimitive", "String", "input", "hint", "prim", "Symbol", "toPrimitive", "undefined", "res", "call", "TypeError", "Number", "createMachine", "_ref", "_ref2", "states", "context", "initial", "actions", "currentState", "currentContext", "send", "event", "currentStateOn", "on", "transitionConfig", "type", "actName", "actionImpl", "nextContextValue"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/node_modules/webpack-dev-server/client/overlay/fsm.js"], "sourcesContent": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/**\n * @typedef {Object} StateDefinitions\n * @property {{[event: string]: { target: string; actions?: Array<string> }}} [on]\n */\n\n/**\n * @typedef {Object} Options\n * @property {{[state: string]: StateDefinitions}} states\n * @property {object} context;\n * @property {string} initial\n */\n\n/**\n * @typedef {Object} Implementation\n * @property {{[actionName: string]: (ctx: object, event: any) => object}} actions\n */\n\n/**\n * A simplified `createMachine` from `@xstate/fsm` with the following differences:\n *\n *  - the returned machine is technically a \"service\". No `interpret(machine).start()` is needed.\n *  - the state definition only support `on` and target must be declared with { target: 'nextState', actions: [] } explicitly.\n *  - event passed to `send` must be an object with `type` property.\n *  - actions implementation will be [assign action](https://xstate.js.org/docs/guides/context.html#assign-action) if you return any value.\n *  Do not return anything if you just want to invoke side effect.\n *\n * The goal of this custom function is to avoid installing the entire `'xstate/fsm'` package, while enabling modeling using\n * state machine. You can copy the first parameter into the editor at https://stately.ai/viz to visualize the state machine.\n *\n * @param {Options} options\n * @param {Implementation} implementation\n */\nfunction createMachine(_ref, _ref2) {\n  var states = _ref.states,\n    context = _ref.context,\n    initial = _ref.initial;\n  var actions = _ref2.actions;\n  var currentState = initial;\n  var currentContext = context;\n  return {\n    send: function send(event) {\n      var currentStateOn = states[currentState].on;\n      var transitionConfig = currentStateOn && currentStateOn[event.type];\n      if (transitionConfig) {\n        currentState = transitionConfig.target;\n        if (transitionConfig.actions) {\n          transitionConfig.actions.forEach(function (actName) {\n            var actionImpl = actions[actName];\n            var nextContextValue = actionImpl && actionImpl(currentContext, event);\n            if (nextContextValue) {\n              currentContext = _objectSpread(_objectSpread({}, currentContext), nextContextValue);\n            }\n          });\n        }\n      }\n    }\n  };\n}\nexport default createMachine;"], "mappings": "AAAA,SAASA,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAEC,cAAc,KAAKI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AACpV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGhB,MAAM,CAACkB,yBAAyB,GAAGlB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAON,MAAM;AAAE;AACzf,SAASO,eAAeA,CAACI,GAAG,EAAEL,GAAG,EAAEM,KAAK,EAAE;EAAEN,GAAG,GAAGO,cAAc,CAACP,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIK,GAAG,EAAE;IAAErB,MAAM,CAACoB,cAAc,CAACC,GAAG,EAAEL,GAAG,EAAE;MAAEM,KAAK,EAAEA,KAAK;MAAEhB,UAAU,EAAE,IAAI;MAAEkB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEJ,GAAG,CAACL,GAAG,CAAC,GAAGM,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAC3O,SAASE,cAAcA,CAACG,GAAG,EAAE;EAAE,IAAIV,GAAG,GAAGW,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAO,OAAOV,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGY,MAAM,CAACZ,GAAG,CAAC;AAAE;AAC1H,SAASW,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,WAAW,CAAC;EAAE,IAAIF,IAAI,KAAKG,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGJ,IAAI,CAACK,IAAI,CAACP,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI,OAAOK,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACP,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGU,MAAM,EAAET,KAAK,CAAC;AAAE;AACxX;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,aAAaA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAClC,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACtBC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,OAAO,GAAGJ,IAAI,CAACI,OAAO;EACxB,IAAIC,OAAO,GAAGJ,KAAK,CAACI,OAAO;EAC3B,IAAIC,YAAY,GAAGF,OAAO;EAC1B,IAAIG,cAAc,GAAGJ,OAAO;EAC5B,OAAO;IACLK,IAAI,EAAE,SAASA,IAAIA,CAACC,KAAK,EAAE;MACzB,IAAIC,cAAc,GAAGR,MAAM,CAACI,YAAY,CAAC,CAACK,EAAE;MAC5C,IAAIC,gBAAgB,GAAGF,cAAc,IAAIA,cAAc,CAACD,KAAK,CAACI,IAAI,CAAC;MACnE,IAAID,gBAAgB,EAAE;QACpBN,YAAY,GAAGM,gBAAgB,CAAC1C,MAAM;QACtC,IAAI0C,gBAAgB,CAACP,OAAO,EAAE;UAC5BO,gBAAgB,CAACP,OAAO,CAAC9B,OAAO,CAAC,UAAUuC,OAAO,EAAE;YAClD,IAAIC,UAAU,GAAGV,OAAO,CAACS,OAAO,CAAC;YACjC,IAAIE,gBAAgB,GAAGD,UAAU,IAAIA,UAAU,CAACR,cAAc,EAAEE,KAAK,CAAC;YACtE,IAAIO,gBAAgB,EAAE;cACpBT,cAAc,GAAGtC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsC,cAAc,CAAC,EAAES,gBAAgB,CAAC;YACrF;UACF,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC;AACH;AACA,eAAejB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}