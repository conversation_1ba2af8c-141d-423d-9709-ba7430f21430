{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\admin\\\\AdminProducts.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaPlus, FaEdit, FaTrash, FaImage } from 'react-icons/fa';\nimport apiService from '../services/api';\nimport CrudTable from './components/CrudTable';\nimport Modal from './components/Modal';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport SuccessMessage from '../components/SuccessMessage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminProducts = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [suppliers, setSuppliers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [modalOpen, setModalOpen] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    category_id: '',\n    supplier_id: '',\n    price: '',\n    sku: '',\n    is_active: true,\n    order_index: 0\n  });\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [submitting, setSubmitting] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  useEffect(() => {\n    fetchData();\n  }, []);\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      const [productsRes, categoriesRes, suppliersRes] = await Promise.all([apiService.admin.getProducts(), apiService.admin.getCategories(), apiService.admin.getSuppliers()]);\n      setProducts(productsRes.data);\n      setCategories(categoriesRes.data);\n      setSuppliers(suppliersRes.data);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n      setError('Failed to load data');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    setEditingProduct(null);\n    setFormData({\n      name: '',\n      description: '',\n      category_id: '',\n      supplier_id: '',\n      price: '',\n      sku: '',\n      is_active: true,\n      order_index: 0\n    });\n    setSelectedFile(null);\n    setModalOpen(true);\n  };\n  const handleEdit = product => {\n    setEditingProduct(product);\n    setFormData({\n      name: product.name || '',\n      description: product.description || '',\n      category_id: product.category_id || '',\n      supplier_id: product.supplier_id || '',\n      price: product.price || '',\n      sku: product.sku || '',\n      is_active: product.is_active !== undefined ? product.is_active : true,\n      order_index: product.order_index || 0\n    });\n    setSelectedFile(null);\n    setModalOpen(true);\n  };\n  const handleDelete = async product => {\n    if (window.confirm(`Are you sure you want to delete \"${product.name}\"?`)) {\n      try {\n        await apiService.admin.deleteProduct(product.id);\n        setSuccess('Product deleted successfully');\n        fetchData();\n      } catch (error) {\n        setError('Failed to delete product');\n      }\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setSubmitting(true);\n    setError(null);\n    try {\n      const submitData = new FormData();\n      Object.keys(formData).forEach(key => {\n        if (formData[key] !== '') {\n          submitData.append(key, formData[key]);\n        }\n      });\n      if (selectedFile) {\n        submitData.append('image', selectedFile);\n      }\n      if (editingProduct) {\n        await apiService.admin.updateProduct(editingProduct.id, submitData);\n        setSuccess('Product updated successfully');\n      } else {\n        await apiService.admin.createProduct(submitData);\n        setSuccess('Product created successfully');\n      }\n      setModalOpen(false);\n      fetchData();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Failed to save product');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleFileChange = e => {\n    setSelectedFile(e.target.files[0]);\n  };\n  const columns = [{\n    key: 'image_url',\n    label: 'Image',\n    type: 'image'\n  }, {\n    key: 'name',\n    label: 'Name',\n    sortable: true\n  }, {\n    key: 'category_name',\n    label: 'Category',\n    sortable: true\n  }, {\n    key: 'supplier_name',\n    label: 'Supplier',\n    sortable: true\n  }, {\n    key: 'price',\n    label: 'Price',\n    render: value => value ? `$${parseFloat(value).toFixed(2)}` : '-'\n  }, {\n    key: 'sku',\n    label: 'SKU'\n  }, {\n    key: 'is_active',\n    label: 'Status',\n    type: 'boolean'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-800 mb-2\",\n        children: \"Products Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Manage your product catalog\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n      message: error,\n      onClose: () => setError(null),\n      className: \"mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(SuccessMessage, {\n      message: success,\n      onClose: () => setSuccess(null),\n      className: \"mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(CrudTable, {\n      title: \"Products\",\n      data: products,\n      columns: columns,\n      loading: loading,\n      onAdd: handleAdd,\n      onEdit: handleEdit,\n      onDelete: handleDelete,\n      addButtonText: \"Add Product\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      isOpen: modalOpen,\n      onClose: () => setModalOpen(false),\n      title: editingProduct ? 'Edit Product' : 'Add New Product',\n      size: \"lg\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"name\",\n              className: \"form-label\",\n              children: \"Product Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"name\",\n              name: \"name\",\n              value: formData.name,\n              onChange: handleInputChange,\n              required: true,\n              className: \"form-input\",\n              placeholder: \"Enter product name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"sku\",\n              className: \"form-label\",\n              children: \"SKU\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"sku\",\n              name: \"sku\",\n              value: formData.sku,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"Enter product SKU\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"category_id\",\n              className: \"form-label\",\n              children: \"Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"category_id\",\n              name: \"category_id\",\n              value: formData.category_id,\n              onChange: handleInputChange,\n              className: \"form-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.id,\n                children: category.name\n              }, category.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"supplier_id\",\n              className: \"form-label\",\n              children: \"Supplier\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"supplier_id\",\n              name: \"supplier_id\",\n              value: formData.supplier_id,\n              onChange: handleInputChange,\n              className: \"form-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Supplier\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), suppliers.map(supplier => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: supplier.id,\n                children: supplier.name\n              }, supplier.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"price\",\n              className: \"form-label\",\n              children: \"Price\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              step: \"0.01\",\n              id: \"price\",\n              name: \"price\",\n              value: formData.price,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"0.00\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order_index\",\n              className: \"form-label\",\n              children: \"Display Order\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"order_index\",\n              name: \"order_index\",\n              value: formData.order_index,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"description\",\n            className: \"form-label\",\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"description\",\n            name: \"description\",\n            value: formData.description,\n            onChange: handleInputChange,\n            rows: 4,\n            className: \"form-textarea\",\n            placeholder: \"Enter product description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"image\",\n            className: \"form-label\",\n            children: \"Product Image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            id: \"image\",\n            accept: \"image/*\",\n            onChange: handleFileChange,\n            className: \"form-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), (editingProduct === null || editingProduct === void 0 ? void 0 : editingProduct.image_url) && !selectedFile && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: editingProduct.image_url,\n              alt: \"Current\",\n              className: \"w-20 h-20 object-cover rounded-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              name: \"is_active\",\n              checked: formData.is_active,\n              onChange: handleInputChange,\n              className: \"rounded border-gray-300 text-primary-green focus:ring-primary-green\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"form-label mb-0\",\n              children: \"Active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-3 pt-6 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setModalOpen(false),\n            className: \"btn btn-outline\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: submitting,\n            className: \"btn btn-primary\",\n            children: submitting ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              size: \"sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this) : editingProduct ? 'Update Product' : 'Create Product'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminProducts, \"LQ9gdf21xz4hPwG26jffw1LhJNw=\");\n_c = AdminProducts;\nexport default AdminProducts;\nvar _c;\n$RefreshReg$(_c, \"AdminProducts\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "FaPlus", "FaEdit", "FaTrash", "FaImage", "apiService", "CrudTable", "Modal", "LoadingSpinner", "ErrorMessage", "SuccessMessage", "jsxDEV", "_jsxDEV", "AdminProducts", "_s", "products", "setProducts", "categories", "setCategories", "suppliers", "setSuppliers", "loading", "setLoading", "modalOpen", "setModalOpen", "editingProduct", "setEditingProduct", "formData", "setFormData", "name", "description", "category_id", "supplier_id", "price", "sku", "is_active", "order_index", "selectedFile", "setSelectedFile", "submitting", "setSubmitting", "error", "setError", "success", "setSuccess", "fetchData", "productsRes", "categoriesRes", "suppliersRes", "Promise", "all", "admin", "getProducts", "getCategories", "getSuppliers", "data", "console", "handleAdd", "handleEdit", "product", "undefined", "handleDelete", "window", "confirm", "deleteProduct", "id", "handleSubmit", "e", "preventDefault", "submitData", "FormData", "Object", "keys", "for<PERSON>ach", "key", "append", "updateProduct", "createProduct", "_error$response", "_error$response$data", "response", "handleInputChange", "value", "type", "checked", "target", "prev", "handleFileChange", "files", "columns", "label", "sortable", "render", "parseFloat", "toFixed", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "onClose", "title", "onAdd", "onEdit", "onDelete", "addButtonText", "isOpen", "size", "onSubmit", "htmlFor", "onChange", "required", "placeholder", "map", "category", "supplier", "step", "rows", "accept", "image_url", "src", "alt", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/admin/AdminProducts.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaPlus, FaEdit, FaTrash, FaImage } from 'react-icons/fa';\nimport apiService from '../services/api';\nimport CrudTable from './components/CrudTable';\nimport Modal from './components/Modal';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport SuccessMessage from '../components/SuccessMessage';\n\nconst AdminProducts = () => {\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [suppliers, setSuppliers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [modalOpen, setModalOpen] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    category_id: '',\n    supplier_id: '',\n    price: '',\n    sku: '',\n    is_active: true,\n    order_index: 0\n  });\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [submitting, setSubmitting] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      const [productsRes, categoriesRes, suppliersRes] = await Promise.all([\n        apiService.admin.getProducts(),\n        apiService.admin.getCategories(),\n        apiService.admin.getSuppliers()\n      ]);\n      \n      setProducts(productsRes.data);\n      setCategories(categoriesRes.data);\n      setSuppliers(suppliersRes.data);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n      setError('Failed to load data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAdd = () => {\n    setEditingProduct(null);\n    setFormData({\n      name: '',\n      description: '',\n      category_id: '',\n      supplier_id: '',\n      price: '',\n      sku: '',\n      is_active: true,\n      order_index: 0\n    });\n    setSelectedFile(null);\n    setModalOpen(true);\n  };\n\n  const handleEdit = (product) => {\n    setEditingProduct(product);\n    setFormData({\n      name: product.name || '',\n      description: product.description || '',\n      category_id: product.category_id || '',\n      supplier_id: product.supplier_id || '',\n      price: product.price || '',\n      sku: product.sku || '',\n      is_active: product.is_active !== undefined ? product.is_active : true,\n      order_index: product.order_index || 0\n    });\n    setSelectedFile(null);\n    setModalOpen(true);\n  };\n\n  const handleDelete = async (product) => {\n    if (window.confirm(`Are you sure you want to delete \"${product.name}\"?`)) {\n      try {\n        await apiService.admin.deleteProduct(product.id);\n        setSuccess('Product deleted successfully');\n        fetchData();\n      } catch (error) {\n        setError('Failed to delete product');\n      }\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSubmitting(true);\n    setError(null);\n\n    try {\n      const submitData = new FormData();\n      Object.keys(formData).forEach(key => {\n        if (formData[key] !== '') {\n          submitData.append(key, formData[key]);\n        }\n      });\n      \n      if (selectedFile) {\n        submitData.append('image', selectedFile);\n      }\n\n      if (editingProduct) {\n        await apiService.admin.updateProduct(editingProduct.id, submitData);\n        setSuccess('Product updated successfully');\n      } else {\n        await apiService.admin.createProduct(submitData);\n        setSuccess('Product created successfully');\n      }\n      \n      setModalOpen(false);\n      fetchData();\n    } catch (error) {\n      setError(error.response?.data?.error || 'Failed to save product');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const handleFileChange = (e) => {\n    setSelectedFile(e.target.files[0]);\n  };\n\n  const columns = [\n    {\n      key: 'image_url',\n      label: 'Image',\n      type: 'image'\n    },\n    {\n      key: 'name',\n      label: 'Name',\n      sortable: true\n    },\n    {\n      key: 'category_name',\n      label: 'Category',\n      sortable: true\n    },\n    {\n      key: 'supplier_name',\n      label: 'Supplier',\n      sortable: true\n    },\n    {\n      key: 'price',\n      label: 'Price',\n      render: (value) => value ? `$${parseFloat(value).toFixed(2)}` : '-'\n    },\n    {\n      key: 'sku',\n      label: 'SKU'\n    },\n    {\n      key: 'is_active',\n      label: 'Status',\n      type: 'boolean'\n    }\n  ];\n\n  return (\n    <div>\n      {/* Page Header */}\n      <div className=\"mb-6\">\n        <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">Products Management</h1>\n        <p className=\"text-gray-600\">Manage your product catalog</p>\n      </div>\n\n      {/* Messages */}\n      {error && <ErrorMessage message={error} onClose={() => setError(null)} className=\"mb-4\" />}\n      {success && <SuccessMessage message={success} onClose={() => setSuccess(null)} className=\"mb-4\" />}\n\n      {/* Products Table */}\n      <CrudTable\n        title=\"Products\"\n        data={products}\n        columns={columns}\n        loading={loading}\n        onAdd={handleAdd}\n        onEdit={handleEdit}\n        onDelete={handleDelete}\n        addButtonText=\"Add Product\"\n      />\n\n      {/* Add/Edit Modal */}\n      <Modal\n        isOpen={modalOpen}\n        onClose={() => setModalOpen(false)}\n        title={editingProduct ? 'Edit Product' : 'Add New Product'}\n        size=\"lg\"\n      >\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"form-group\">\n              <label htmlFor=\"name\" className=\"form-label\">Product Name *</label>\n              <input\n                type=\"text\"\n                id=\"name\"\n                name=\"name\"\n                value={formData.name}\n                onChange={handleInputChange}\n                required\n                className=\"form-input\"\n                placeholder=\"Enter product name\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"sku\" className=\"form-label\">SKU</label>\n              <input\n                type=\"text\"\n                id=\"sku\"\n                name=\"sku\"\n                value={formData.sku}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"Enter product SKU\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"category_id\" className=\"form-label\">Category</label>\n              <select\n                id=\"category_id\"\n                name=\"category_id\"\n                value={formData.category_id}\n                onChange={handleInputChange}\n                className=\"form-select\"\n              >\n                <option value=\"\">Select Category</option>\n                {categories.map(category => (\n                  <option key={category.id} value={category.id}>\n                    {category.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"supplier_id\" className=\"form-label\">Supplier</label>\n              <select\n                id=\"supplier_id\"\n                name=\"supplier_id\"\n                value={formData.supplier_id}\n                onChange={handleInputChange}\n                className=\"form-select\"\n              >\n                <option value=\"\">Select Supplier</option>\n                {suppliers.map(supplier => (\n                  <option key={supplier.id} value={supplier.id}>\n                    {supplier.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"price\" className=\"form-label\">Price</label>\n              <input\n                type=\"number\"\n                step=\"0.01\"\n                id=\"price\"\n                name=\"price\"\n                value={formData.price}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"0.00\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"order_index\" className=\"form-label\">Display Order</label>\n              <input\n                type=\"number\"\n                id=\"order_index\"\n                name=\"order_index\"\n                value={formData.order_index}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"0\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"description\" className=\"form-label\">Description</label>\n            <textarea\n              id=\"description\"\n              name=\"description\"\n              value={formData.description}\n              onChange={handleInputChange}\n              rows={4}\n              className=\"form-textarea\"\n              placeholder=\"Enter product description\"\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"image\" className=\"form-label\">Product Image</label>\n            <input\n              type=\"file\"\n              id=\"image\"\n              accept=\"image/*\"\n              onChange={handleFileChange}\n              className=\"form-input\"\n            />\n            {editingProduct?.image_url && !selectedFile && (\n              <div className=\"mt-2\">\n                <img\n                  src={editingProduct.image_url}\n                  alt=\"Current\"\n                  className=\"w-20 h-20 object-cover rounded-lg\"\n                />\n              </div>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label className=\"flex items-center space-x-2\">\n              <input\n                type=\"checkbox\"\n                name=\"is_active\"\n                checked={formData.is_active}\n                onChange={handleInputChange}\n                className=\"rounded border-gray-300 text-primary-green focus:ring-primary-green\"\n              />\n              <span className=\"form-label mb-0\">Active</span>\n            </label>\n          </div>\n\n          <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200\">\n            <button\n              type=\"button\"\n              onClick={() => setModalOpen(false)}\n              className=\"btn btn-outline\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={submitting}\n              className=\"btn btn-primary\"\n            >\n              {submitting ? (\n                <LoadingSpinner size=\"sm\" />\n              ) : (\n                editingProduct ? 'Update Product' : 'Create Product'\n              )}\n            </button>\n          </div>\n        </form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default AdminProducts;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AACjE,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC;IACvC+B,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,GAAG,EAAE,EAAE;IACPC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd8C,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFvB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACwB,WAAW,EAAEC,aAAa,EAAEC,YAAY,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnE7C,UAAU,CAAC8C,KAAK,CAACC,WAAW,CAAC,CAAC,EAC9B/C,UAAU,CAAC8C,KAAK,CAACE,aAAa,CAAC,CAAC,EAChChD,UAAU,CAAC8C,KAAK,CAACG,YAAY,CAAC,CAAC,CAChC,CAAC;MAEFtC,WAAW,CAAC8B,WAAW,CAACS,IAAI,CAAC;MAC7BrC,aAAa,CAAC6B,aAAa,CAACQ,IAAI,CAAC;MACjCnC,YAAY,CAAC4B,YAAY,CAACO,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOd,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmC,SAAS,GAAGA,CAAA,KAAM;IACtB/B,iBAAiB,CAAC,IAAI,CAAC;IACvBE,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,EAAE;MACPC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE;IACf,CAAC,CAAC;IACFE,eAAe,CAAC,IAAI,CAAC;IACrBd,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMkC,UAAU,GAAIC,OAAO,IAAK;IAC9BjC,iBAAiB,CAACiC,OAAO,CAAC;IAC1B/B,WAAW,CAAC;MACVC,IAAI,EAAE8B,OAAO,CAAC9B,IAAI,IAAI,EAAE;MACxBC,WAAW,EAAE6B,OAAO,CAAC7B,WAAW,IAAI,EAAE;MACtCC,WAAW,EAAE4B,OAAO,CAAC5B,WAAW,IAAI,EAAE;MACtCC,WAAW,EAAE2B,OAAO,CAAC3B,WAAW,IAAI,EAAE;MACtCC,KAAK,EAAE0B,OAAO,CAAC1B,KAAK,IAAI,EAAE;MAC1BC,GAAG,EAAEyB,OAAO,CAACzB,GAAG,IAAI,EAAE;MACtBC,SAAS,EAAEwB,OAAO,CAACxB,SAAS,KAAKyB,SAAS,GAAGD,OAAO,CAACxB,SAAS,GAAG,IAAI;MACrEC,WAAW,EAAEuB,OAAO,CAACvB,WAAW,IAAI;IACtC,CAAC,CAAC;IACFE,eAAe,CAAC,IAAI,CAAC;IACrBd,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMqC,YAAY,GAAG,MAAOF,OAAO,IAAK;IACtC,IAAIG,MAAM,CAACC,OAAO,CAAC,oCAAoCJ,OAAO,CAAC9B,IAAI,IAAI,CAAC,EAAE;MACxE,IAAI;QACF,MAAMxB,UAAU,CAAC8C,KAAK,CAACa,aAAa,CAACL,OAAO,CAACM,EAAE,CAAC;QAChDrB,UAAU,CAAC,8BAA8B,CAAC;QAC1CC,SAAS,CAAC,CAAC;MACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdC,QAAQ,CAAC,0BAA0B,CAAC;MACtC;IACF;EACF,CAAC;EAED,MAAMwB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB5B,aAAa,CAAC,IAAI,CAAC;IACnBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAM2B,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;MACjCC,MAAM,CAACC,IAAI,CAAC7C,QAAQ,CAAC,CAAC8C,OAAO,CAACC,GAAG,IAAI;QACnC,IAAI/C,QAAQ,CAAC+C,GAAG,CAAC,KAAK,EAAE,EAAE;UACxBL,UAAU,CAACM,MAAM,CAACD,GAAG,EAAE/C,QAAQ,CAAC+C,GAAG,CAAC,CAAC;QACvC;MACF,CAAC,CAAC;MAEF,IAAIrC,YAAY,EAAE;QAChBgC,UAAU,CAACM,MAAM,CAAC,OAAO,EAAEtC,YAAY,CAAC;MAC1C;MAEA,IAAIZ,cAAc,EAAE;QAClB,MAAMpB,UAAU,CAAC8C,KAAK,CAACyB,aAAa,CAACnD,cAAc,CAACwC,EAAE,EAAEI,UAAU,CAAC;QACnEzB,UAAU,CAAC,8BAA8B,CAAC;MAC5C,CAAC,MAAM;QACL,MAAMvC,UAAU,CAAC8C,KAAK,CAAC0B,aAAa,CAACR,UAAU,CAAC;QAChDzB,UAAU,CAAC,8BAA8B,CAAC;MAC5C;MAEApB,YAAY,CAAC,KAAK,CAAC;MACnBqB,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;MAAA,IAAAqC,eAAA,EAAAC,oBAAA;MACdrC,QAAQ,CAAC,EAAAoC,eAAA,GAAArC,KAAK,CAACuC,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBvB,IAAI,cAAAwB,oBAAA,uBAApBA,oBAAA,CAAsBtC,KAAK,KAAI,wBAAwB,CAAC;IACnE,CAAC,SAAS;MACRD,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMyC,iBAAiB,GAAId,CAAC,IAAK;IAC/B,MAAM;MAAEtC,IAAI;MAAEqD,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGjB,CAAC,CAACkB,MAAM;IAC/CzD,WAAW,CAAC0D,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACzD,IAAI,GAAGsD,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,gBAAgB,GAAIpB,CAAC,IAAK;IAC9B7B,eAAe,CAAC6B,CAAC,CAACkB,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;EACpC,CAAC;EAED,MAAMC,OAAO,GAAG,CACd;IACEf,GAAG,EAAE,WAAW;IAChBgB,KAAK,EAAE,OAAO;IACdP,IAAI,EAAE;EACR,CAAC,EACD;IACET,GAAG,EAAE,MAAM;IACXgB,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEjB,GAAG,EAAE,eAAe;IACpBgB,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEjB,GAAG,EAAE,eAAe;IACpBgB,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEjB,GAAG,EAAE,OAAO;IACZgB,KAAK,EAAE,OAAO;IACdE,MAAM,EAAGV,KAAK,IAAKA,KAAK,GAAG,IAAIW,UAAU,CAACX,KAAK,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;EAClE,CAAC,EACD;IACEpB,GAAG,EAAE,KAAK;IACVgB,KAAK,EAAE;EACT,CAAC,EACD;IACEhB,GAAG,EAAE,WAAW;IAChBgB,KAAK,EAAE,QAAQ;IACfP,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACEvE,OAAA;IAAAmF,QAAA,gBAEEnF,OAAA;MAAKoF,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBnF,OAAA;QAAIoF,SAAS,EAAC,uCAAuC;QAAAD,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9ExF,OAAA;QAAGoF,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAC;MAA2B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,EAGL3D,KAAK,iBAAI7B,OAAA,CAACH,YAAY;MAAC4F,OAAO,EAAE5D,KAAM;MAAC6D,OAAO,EAAEA,CAAA,KAAM5D,QAAQ,CAAC,IAAI,CAAE;MAACsD,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACzFzD,OAAO,iBAAI/B,OAAA,CAACF,cAAc;MAAC2F,OAAO,EAAE1D,OAAQ;MAAC2D,OAAO,EAAEA,CAAA,KAAM1D,UAAU,CAAC,IAAI,CAAE;MAACoD,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGlGxF,OAAA,CAACN,SAAS;MACRiG,KAAK,EAAC,UAAU;MAChBhD,IAAI,EAAExC,QAAS;MACf0E,OAAO,EAAEA,OAAQ;MACjBpE,OAAO,EAAEA,OAAQ;MACjBmF,KAAK,EAAE/C,SAAU;MACjBgD,MAAM,EAAE/C,UAAW;MACnBgD,QAAQ,EAAE7C,YAAa;MACvB8C,aAAa,EAAC;IAAa;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAGFxF,OAAA,CAACL,KAAK;MACJqG,MAAM,EAAErF,SAAU;MAClB+E,OAAO,EAAEA,CAAA,KAAM9E,YAAY,CAAC,KAAK,CAAE;MACnC+E,KAAK,EAAE9E,cAAc,GAAG,cAAc,GAAG,iBAAkB;MAC3DoF,IAAI,EAAC,IAAI;MAAAd,QAAA,eAETnF,OAAA;QAAMkG,QAAQ,EAAE5C,YAAa;QAAC8B,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACjDnF,OAAA;UAAKoF,SAAS,EAAC,uCAAuC;UAAAD,QAAA,gBACpDnF,OAAA;YAAKoF,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzBnF,OAAA;cAAOmG,OAAO,EAAC,MAAM;cAACf,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnExF,OAAA;cACEuE,IAAI,EAAC,MAAM;cACXlB,EAAE,EAAC,MAAM;cACTpC,IAAI,EAAC,MAAM;cACXqD,KAAK,EAAEvD,QAAQ,CAACE,IAAK;cACrBmF,QAAQ,EAAE/B,iBAAkB;cAC5BgC,QAAQ;cACRjB,SAAS,EAAC,YAAY;cACtBkB,WAAW,EAAC;YAAoB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxF,OAAA;YAAKoF,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzBnF,OAAA;cAAOmG,OAAO,EAAC,KAAK;cAACf,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvDxF,OAAA;cACEuE,IAAI,EAAC,MAAM;cACXlB,EAAE,EAAC,KAAK;cACRpC,IAAI,EAAC,KAAK;cACVqD,KAAK,EAAEvD,QAAQ,CAACO,GAAI;cACpB8E,QAAQ,EAAE/B,iBAAkB;cAC5Be,SAAS,EAAC,YAAY;cACtBkB,WAAW,EAAC;YAAmB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxF,OAAA;YAAKoF,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzBnF,OAAA;cAAOmG,OAAO,EAAC,aAAa;cAACf,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpExF,OAAA;cACEqD,EAAE,EAAC,aAAa;cAChBpC,IAAI,EAAC,aAAa;cAClBqD,KAAK,EAAEvD,QAAQ,CAACI,WAAY;cAC5BiF,QAAQ,EAAE/B,iBAAkB;cAC5Be,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAEvBnF,OAAA;gBAAQsE,KAAK,EAAC,EAAE;gBAAAa,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCnF,UAAU,CAACkG,GAAG,CAACC,QAAQ,iBACtBxG,OAAA;gBAA0BsE,KAAK,EAAEkC,QAAQ,CAACnD,EAAG;gBAAA8B,QAAA,EAC1CqB,QAAQ,CAACvF;cAAI,GADHuF,QAAQ,CAACnD,EAAE;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENxF,OAAA;YAAKoF,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzBnF,OAAA;cAAOmG,OAAO,EAAC,aAAa;cAACf,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpExF,OAAA;cACEqD,EAAE,EAAC,aAAa;cAChBpC,IAAI,EAAC,aAAa;cAClBqD,KAAK,EAAEvD,QAAQ,CAACK,WAAY;cAC5BgF,QAAQ,EAAE/B,iBAAkB;cAC5Be,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAEvBnF,OAAA;gBAAQsE,KAAK,EAAC,EAAE;gBAAAa,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCjF,SAAS,CAACgG,GAAG,CAACE,QAAQ,iBACrBzG,OAAA;gBAA0BsE,KAAK,EAAEmC,QAAQ,CAACpD,EAAG;gBAAA8B,QAAA,EAC1CsB,QAAQ,CAACxF;cAAI,GADHwF,QAAQ,CAACpD,EAAE;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENxF,OAAA;YAAKoF,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzBnF,OAAA;cAAOmG,OAAO,EAAC,OAAO;cAACf,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3DxF,OAAA;cACEuE,IAAI,EAAC,QAAQ;cACbmC,IAAI,EAAC,MAAM;cACXrD,EAAE,EAAC,OAAO;cACVpC,IAAI,EAAC,OAAO;cACZqD,KAAK,EAAEvD,QAAQ,CAACM,KAAM;cACtB+E,QAAQ,EAAE/B,iBAAkB;cAC5Be,SAAS,EAAC,YAAY;cACtBkB,WAAW,EAAC;YAAM;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxF,OAAA;YAAKoF,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzBnF,OAAA;cAAOmG,OAAO,EAAC,aAAa;cAACf,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzExF,OAAA;cACEuE,IAAI,EAAC,QAAQ;cACblB,EAAE,EAAC,aAAa;cAChBpC,IAAI,EAAC,aAAa;cAClBqD,KAAK,EAAEvD,QAAQ,CAACS,WAAY;cAC5B4E,QAAQ,EAAE/B,iBAAkB;cAC5Be,SAAS,EAAC,YAAY;cACtBkB,WAAW,EAAC;YAAG;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxF,OAAA;UAAKoF,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzBnF,OAAA;YAAOmG,OAAO,EAAC,aAAa;YAACf,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvExF,OAAA;YACEqD,EAAE,EAAC,aAAa;YAChBpC,IAAI,EAAC,aAAa;YAClBqD,KAAK,EAAEvD,QAAQ,CAACG,WAAY;YAC5BkF,QAAQ,EAAE/B,iBAAkB;YAC5BsC,IAAI,EAAE,CAAE;YACRvB,SAAS,EAAC,eAAe;YACzBkB,WAAW,EAAC;UAA2B;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxF,OAAA;UAAKoF,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzBnF,OAAA;YAAOmG,OAAO,EAAC,OAAO;YAACf,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnExF,OAAA;YACEuE,IAAI,EAAC,MAAM;YACXlB,EAAE,EAAC,OAAO;YACVuD,MAAM,EAAC,SAAS;YAChBR,QAAQ,EAAEzB,gBAAiB;YAC3BS,SAAS,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,EACD,CAAA3E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEgG,SAAS,KAAI,CAACpF,YAAY,iBACzCzB,OAAA;YAAKoF,SAAS,EAAC,MAAM;YAAAD,QAAA,eACnBnF,OAAA;cACE8G,GAAG,EAAEjG,cAAc,CAACgG,SAAU;cAC9BE,GAAG,EAAC,SAAS;cACb3B,SAAS,EAAC;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENxF,OAAA;UAAKoF,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzBnF,OAAA;YAAOoF,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC5CnF,OAAA;cACEuE,IAAI,EAAC,UAAU;cACftD,IAAI,EAAC,WAAW;cAChBuD,OAAO,EAAEzD,QAAQ,CAACQ,SAAU;cAC5B6E,QAAQ,EAAE/B,iBAAkB;cAC5Be,SAAS,EAAC;YAAqE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,eACFxF,OAAA;cAAMoF,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENxF,OAAA;UAAKoF,SAAS,EAAC,0DAA0D;UAAAD,QAAA,gBACvEnF,OAAA;YACEuE,IAAI,EAAC,QAAQ;YACbyC,OAAO,EAAEA,CAAA,KAAMpG,YAAY,CAAC,KAAK,CAAE;YACnCwE,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAC5B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxF,OAAA;YACEuE,IAAI,EAAC,QAAQ;YACb0C,QAAQ,EAAEtF,UAAW;YACrByD,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAE1BxD,UAAU,gBACT3B,OAAA,CAACJ,cAAc;cAACqG,IAAI,EAAC;YAAI;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAE5B3E,cAAc,GAAG,gBAAgB,GAAG;UACrC;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACtF,EAAA,CA/WID,aAAa;AAAAiH,EAAA,GAAbjH,aAAa;AAiXnB,eAAeA,aAAa;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}