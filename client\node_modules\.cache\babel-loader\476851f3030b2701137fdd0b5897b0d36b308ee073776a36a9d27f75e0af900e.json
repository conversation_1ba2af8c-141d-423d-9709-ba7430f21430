{"ast": null, "code": "import { addDomEvent } from './add-dom-event.mjs';\nimport { addPointerInfo } from './event-info.mjs';\nfunction addPointerEvent(target, eventName, handler, options) {\n  return addDomEvent(target, eventName, addPointerInfo(handler), options);\n}\nexport { addPointerEvent };", "map": {"version": 3, "names": ["addDomEvent", "addPointerInfo", "addPointerEvent", "target", "eventName", "handler", "options"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs"], "sourcesContent": ["import { addDomEvent } from './add-dom-event.mjs';\nimport { addPointerInfo } from './event-info.mjs';\n\nfunction addPointerEvent(target, eventName, handler, options) {\n    return addDomEvent(target, eventName, addPointerInfo(handler), options);\n}\n\nexport { addPointerEvent };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,qBAAqB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AAEjD,SAASC,eAAeA,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC1D,OAAON,WAAW,CAACG,MAAM,EAAEC,SAAS,EAAEH,cAAc,CAACI,OAAO,CAAC,EAAEC,OAAO,CAAC;AAC3E;AAEA,SAASJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}