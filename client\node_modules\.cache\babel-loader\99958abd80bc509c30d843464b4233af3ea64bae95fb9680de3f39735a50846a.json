{"ast": null, "code": "import { createContext } from 'react';\n\n/**\n * @public\n */\nconst MotionConfigContext = createContext({\n  transformPagePoint: p => p,\n  isStatic: false,\n  reducedMotion: \"never\"\n});\nexport { MotionConfigContext };", "map": {"version": 3, "names": ["createContext", "MotionConfigContext", "transformPagePoint", "p", "isStatic", "reducedMotion"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs"], "sourcesContent": ["import { createContext } from 'react';\n\n/**\n * @public\n */\nconst MotionConfigContext = createContext({\n    transformPagePoint: (p) => p,\n    isStatic: false,\n    reducedMotion: \"never\",\n});\n\nexport { MotionConfigContext };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;;AAErC;AACA;AACA;AACA,MAAMC,mBAAmB,GAAGD,aAAa,CAAC;EACtCE,kBAAkB,EAAGC,CAAC,IAAKA,CAAC;EAC5BC,QAAQ,EAAE,KAAK;EACfC,aAAa,EAAE;AACnB,CAAC,CAAC;AAEF,SAASJ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}