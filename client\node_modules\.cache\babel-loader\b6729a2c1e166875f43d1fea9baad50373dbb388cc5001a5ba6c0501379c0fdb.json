{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\components\\\\ErrorMessage.js\";\nimport React from 'react';\nimport { FaExclamationTriangle, FaTimes } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ErrorMessage = ({\n  message,\n  onClose,\n  className = ''\n}) => {\n  if (!message) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-red-50 border border-red-200 rounded-lg p-4 ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start\",\n      children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n        className: \"text-red-500 mt-0.5 mr-3 flex-shrink-0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-800 text-sm\",\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), onClose && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"text-red-500 hover:text-red-700 ml-3 flex-shrink-0\",\n        children: /*#__PURE__*/_jsxDEV(FaTimes, {\n          size: 14\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = ErrorMessage;\nexport default ErrorMessage;\nvar _c;\n$RefreshReg$(_c, \"ErrorMessage\");", "map": {"version": 3, "names": ["React", "FaExclamationTriangle", "FaTimes", "jsxDEV", "_jsxDEV", "ErrorMessage", "message", "onClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "size", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/components/ErrorMessage.js"], "sourcesContent": ["import React from 'react';\nimport { FaExclamationTriangle, FaTimes } from 'react-icons/fa';\n\nconst ErrorMessage = ({ message, onClose, className = '' }) => {\n  if (!message) return null;\n\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <FaExclamationTriangle className=\"text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"text-red-500 hover:text-red-700 ml-3 flex-shrink-0\"\n          >\n            <FaTimes size={14} />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ErrorMessage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,qBAAqB,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,YAAY,GAAGA,CAAC;EAAEC,OAAO;EAAEC,OAAO;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAC7D,IAAI,CAACF,OAAO,EAAE,OAAO,IAAI;EAEzB,oBACEF,OAAA;IAAKI,SAAS,EAAE,kDAAkDA,SAAS,EAAG;IAAAC,QAAA,eAC5EL,OAAA;MAAKI,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BL,OAAA,CAACH,qBAAqB;QAACO,SAAS,EAAC;MAAwC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5ET,OAAA;QAAKI,SAAS,EAAC,QAAQ;QAAAC,QAAA,eACrBL,OAAA;UAAGI,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAEH;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,EACLN,OAAO,iBACNH,OAAA;QACEU,OAAO,EAAEP,OAAQ;QACjBC,SAAS,EAAC,oDAAoD;QAAAC,QAAA,eAE9DL,OAAA,CAACF,OAAO;UAACa,IAAI,EAAE;QAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GArBIX,YAAY;AAuBlB,eAAeA,YAAY;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}