{"ast": null, "code": "import { useInsertionEffect } from 'react';\nfunction useMotionValueEvent(value, event, callback) {\n  /**\n   * useInsertionEffect will create subscriptions before any other\n   * effects will run. Effects run upwards through the tree so it\n   * can be that binding a useLayoutEffect higher up the tree can\n   * miss changes from lower down the tree.\n   */\n  useInsertionEffect(() => value.on(event, callback), [value, event, callback]);\n}\nexport { useMotionValueEvent };", "map": {"version": 3, "names": ["useInsertionEffect", "useMotionValueEvent", "value", "event", "callback", "on"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/node_modules/framer-motion/dist/es/utils/use-motion-value-event.mjs"], "sourcesContent": ["import { useInsertionEffect } from 'react';\n\nfunction useMotionValueEvent(value, event, callback) {\n    /**\n     * useInsertionEffect will create subscriptions before any other\n     * effects will run. Effects run upwards through the tree so it\n     * can be that binding a useLayoutEffect higher up the tree can\n     * miss changes from lower down the tree.\n     */\n    useInsertionEffect(() => value.on(event, callback), [value, event, callback]);\n}\n\nexport { useMotionValueEvent };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,OAAO;AAE1C,SAASC,mBAAmBA,CAACC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EACjD;AACJ;AACA;AACA;AACA;AACA;EACIJ,kBAAkB,CAAC,MAAME,KAAK,CAACG,EAAE,CAACF,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAACF,KAAK,EAAEC,KAAK,EAAEC,QAAQ,CAAC,CAAC;AACjF;AAEA,SAASH,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}