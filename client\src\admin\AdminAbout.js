import React, { useState, useEffect } from 'react';
import apiService from '../services/api';
import CrudTable from './components/CrudTable';
import Modal from './components/Modal';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import SuccessMessage from '../components/SuccessMessage';

const AdminAbout = () => {
  const [aboutContent, setAboutContent] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalOpen, setModalOpen] = useState(false);
  const [editingContent, setEditingContent] = useState(null);
  const [formData, setFormData] = useState({
    section: '',
    title: '',
    content: '',
    order_index: 0
  });
  const [selectedFile, setSelectedFile] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  useEffect(() => {
    fetchAboutContent();
  }, []);

  const fetchAboutContent = async () => {
    try {
      setLoading(true);
      const response = await apiService.admin.getAboutContent();
      setAboutContent(response.data);
    } catch (error) {
      setError('Failed to load about content');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingContent(null);
    setFormData({
      section: '',
      title: '',
      content: '',
      order_index: 0
    });
    setSelectedFile(null);
    setModalOpen(true);
  };

  const handleEdit = (content) => {
    setEditingContent(content);
    setFormData({
      section: content.section || '',
      title: content.title || '',
      content: content.content || '',
      order_index: content.order_index || 0
    });
    setSelectedFile(null);
    setModalOpen(true);
  };

  const handleDelete = async (content) => {
    if (window.confirm(`Are you sure you want to delete "${content.title || content.section}"?`)) {
      try {
        await apiService.admin.deleteAboutContent(content.id);
        setSuccess('Content deleted successfully');
        fetchAboutContent();
      } catch (error) {
        setError('Failed to delete content');
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      const submitData = new FormData();
      Object.keys(formData).forEach(key => {
        if (formData[key] !== '') {
          submitData.append(key, formData[key]);
        }
      });
      
      if (selectedFile) {
        submitData.append('image', selectedFile);
      }

      if (editingContent) {
        await apiService.admin.updateAboutContent(editingContent.id, submitData);
        setSuccess('Content updated successfully');
      } else {
        await apiService.admin.createAboutContent(submitData);
        setSuccess('Content created successfully');
      }
      
      setModalOpen(false);
      fetchAboutContent();
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to save content');
    } finally {
      setSubmitting(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e) => {
    setSelectedFile(e.target.files[0]);
  };

  const columns = [
    { key: 'section', label: 'Section', sortable: true },
    { key: 'title', label: 'Title', sortable: true },
    { key: 'content', label: 'Content', type: 'text' },
    { key: 'image_url', label: 'Image', type: 'image' },
    { key: 'order_index', label: 'Order', sortable: true }
  ];

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">About Content Management</h1>
        <p className="text-gray-600">Manage about us sections and content</p>
      </div>

      {error && <ErrorMessage message={error} onClose={() => setError(null)} className="mb-4" />}
      {success && <SuccessMessage message={success} onClose={() => setSuccess(null)} className="mb-4" />}

      <CrudTable
        title="About Content"
        data={aboutContent}
        columns={columns}
        loading={loading}
        onAdd={handleAdd}
        onEdit={handleEdit}
        onDelete={handleDelete}
        addButtonText="Add Content Section"
      />

      <Modal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        title={editingContent ? 'Edit About Content' : 'Add About Content'}
        size="lg"
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="form-group">
              <label htmlFor="section" className="form-label">Section Name *</label>
              <input
                type="text"
                id="section"
                name="section"
                value={formData.section}
                onChange={handleInputChange}
                required
                className="form-input"
                placeholder="e.g., company-history, mission, vision"
              />
            </div>

            <div className="form-group">
              <label htmlFor="order_index" className="form-label">Display Order</label>
              <input
                type="number"
                id="order_index"
                name="order_index"
                value={formData.order_index}
                onChange={handleInputChange}
                className="form-input"
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="title" className="form-label">Title</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Section title"
            />
          </div>

          <div className="form-group">
            <label htmlFor="content" className="form-label">Content</label>
            <textarea
              id="content"
              name="content"
              value={formData.content}
              onChange={handleInputChange}
              rows={8}
              className="form-textarea"
              placeholder="Enter content (HTML supported)"
            />
          </div>

          <div className="form-group">
            <label htmlFor="image" className="form-label">Section Image</label>
            <input
              type="file"
              id="image"
              accept="image/*"
              onChange={handleFileChange}
              className="form-input"
            />
            {editingContent?.image_url && !selectedFile && (
              <div className="mt-2">
                <img
                  src={editingContent.image_url}
                  alt="Current"
                  className="w-32 h-32 object-cover rounded-lg"
                />
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={() => setModalOpen(false)}
              className="btn btn-outline"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={submitting}
              className="btn btn-primary"
            >
              {submitting ? <LoadingSpinner size="sm" /> : (editingContent ? 'Update' : 'Create')}
            </button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default AdminAbout;
