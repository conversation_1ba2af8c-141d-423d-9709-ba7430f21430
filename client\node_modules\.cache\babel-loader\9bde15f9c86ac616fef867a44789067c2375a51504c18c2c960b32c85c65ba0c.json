{"ast": null, "code": "import * as React from 'react';\nimport { rootProjectionNode } from './node/HTMLProjectionNode.mjs';\nfunction useResetProjection() {\n  const reset = React.useCallback(() => {\n    const root = rootProjectionNode.current;\n    if (!root) return;\n    root.resetTree();\n  }, []);\n  return reset;\n}\nexport { useResetProjection };", "map": {"version": 3, "names": ["React", "rootProjectionNode", "useResetProjection", "reset", "useCallback", "root", "current", "resetTree"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/node_modules/framer-motion/dist/es/projection/use-reset-projection.mjs"], "sourcesContent": ["import * as React from 'react';\nimport { rootProjectionNode } from './node/HTMLProjectionNode.mjs';\n\nfunction useResetProjection() {\n    const reset = React.useCallback(() => {\n        const root = rootProjectionNode.current;\n        if (!root)\n            return;\n        root.resetTree();\n    }, []);\n    return reset;\n}\n\nexport { useResetProjection };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,+BAA+B;AAElE,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,MAAMC,KAAK,GAAGH,KAAK,CAACI,WAAW,CAAC,MAAM;IAClC,MAAMC,IAAI,GAAGJ,kBAAkB,CAACK,OAAO;IACvC,IAAI,CAACD,IAAI,EACL;IACJA,IAAI,CAACE,SAAS,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EACN,OAAOJ,KAAK;AAChB;AAEA,SAASD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}