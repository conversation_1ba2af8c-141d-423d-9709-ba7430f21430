const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, '../database/test.db');
console.log('Testing SQLite with path:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('SQLite error:', err);
  } else {
    console.log('SQLite connection successful!');
    
    // Test creating a simple table
    db.run('CREATE TABLE IF NOT EXISTS test (id INTEGER PRIMARY KEY, name TEXT)', (err) => {
      if (err) {
        console.error('Table creation error:', err);
      } else {
        console.log('Table created successfully!');
      }
      
      db.close((err) => {
        if (err) {
          console.error('Close error:', err);
        } else {
          console.log('Database closed successfully!');
        }
      });
    });
  }
});
