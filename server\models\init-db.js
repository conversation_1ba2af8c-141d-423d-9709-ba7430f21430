const JsonDatabase = require('./JsonDatabase');
require('dotenv').config();

async function initializeDatabase() {
  const db = new JsonDatabase();

  try {
    await db.connect();
    await db.initializeTables();
    console.log('Database initialized successfully!');
  } catch (error) {
    console.error('Failed to initialize database:', error);
    process.exit(1);
  } finally {
    await db.close();
  }
}

// Run if this file is executed directly
if (require.main === module) {
  initializeDatabase();
}

module.exports = initializeDatabase;
