{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\contexts\\\\SiteDataContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SiteDataContext = /*#__PURE__*/createContext();\nexport const useSiteData = () => {\n  _s();\n  const context = useContext(SiteDataContext);\n  if (!context) {\n    throw new Error('useSiteData must be used within a SiteDataProvider');\n  }\n  return context;\n};\n_s(useSiteData, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const SiteDataProvider = ({\n  children\n}) => {\n  _s2();\n  const [siteInfo, setSiteInfo] = useState({\n    settings: {},\n    contact: {}\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchSiteInfo();\n  }, []);\n  const fetchSiteInfo = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.public.getSiteInfo();\n      setSiteInfo(response.data);\n      setError(null);\n    } catch (error) {\n      console.error('Failed to fetch site info:', error);\n      setError('Failed to load site information');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const refreshSiteInfo = () => {\n    fetchSiteInfo();\n  };\n  const value = {\n    siteInfo,\n    loading,\n    error,\n    refreshSiteInfo\n  };\n  return /*#__PURE__*/_jsxDEV(SiteDataContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_s2(SiteDataProvider, \"h1b4UIEcopqGsxOcGmvzjduTD1U=\");\n_c = SiteDataProvider;\nvar _c;\n$RefreshReg$(_c, \"SiteDataProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "apiService", "jsxDEV", "_jsxDEV", "SiteDataContext", "useSiteData", "_s", "context", "Error", "SiteDataProvider", "children", "_s2", "siteInfo", "setSiteInfo", "settings", "contact", "loading", "setLoading", "error", "setError", "fetchSiteInfo", "response", "public", "getSiteInfo", "data", "console", "refreshSiteInfo", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/contexts/SiteDataContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport apiService from '../services/api';\n\nconst SiteDataContext = createContext();\n\nexport const useSiteData = () => {\n  const context = useContext(SiteDataContext);\n  if (!context) {\n    throw new Error('useSiteData must be used within a SiteDataProvider');\n  }\n  return context;\n};\n\nexport const SiteDataProvider = ({ children }) => {\n  const [siteInfo, setSiteInfo] = useState({\n    settings: {},\n    contact: {}\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchSiteInfo();\n  }, []);\n\n  const fetchSiteInfo = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.public.getSiteInfo();\n      setSiteInfo(response.data);\n      setError(null);\n    } catch (error) {\n      console.error('Failed to fetch site info:', error);\n      setError('Failed to load site information');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const refreshSiteInfo = () => {\n    fetchSiteInfo();\n  };\n\n  const value = {\n    siteInfo,\n    loading,\n    error,\n    refreshSiteInfo\n  };\n\n  return (\n    <SiteDataContext.Provider value={value}>\n      {children}\n    </SiteDataContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,eAAe,gBAAGP,aAAa,CAAC,CAAC;AAEvC,OAAO,MAAMQ,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,OAAO,GAAGT,UAAU,CAACM,eAAe,CAAC;EAC3C,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;EACvE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,WAAW;AAQxB,OAAO,MAAMI,gBAAgB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAChD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC;IACvCe,QAAQ,EAAE,CAAC,CAAC;IACZC,OAAO,EAAE,CAAC;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdoB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMpB,UAAU,CAACqB,MAAM,CAACC,WAAW,CAAC,CAAC;MACtDV,WAAW,CAACQ,QAAQ,CAACG,IAAI,CAAC;MAC1BL,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAAC,iCAAiC,CAAC;IAC7C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,eAAe,GAAGA,CAAA,KAAM;IAC5BN,aAAa,CAAC,CAAC;EACjB,CAAC;EAED,MAAMO,KAAK,GAAG;IACZf,QAAQ;IACRI,OAAO;IACPE,KAAK;IACLQ;EACF,CAAC;EAED,oBACEvB,OAAA,CAACC,eAAe,CAACwB,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAjB,QAAA,EACpCA;EAAQ;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAE/B,CAAC;AAACrB,GAAA,CA1CWF,gBAAgB;AAAAwB,EAAA,GAAhBxB,gBAAgB;AAAA,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}