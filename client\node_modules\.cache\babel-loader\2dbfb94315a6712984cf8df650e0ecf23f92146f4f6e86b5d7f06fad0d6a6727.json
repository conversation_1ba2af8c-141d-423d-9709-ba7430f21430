{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\admin\\\\AdminNews.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport apiService from '../services/api';\nimport CrudTable from './components/CrudTable';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport SuccessMessage from '../components/SuccessMessage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminNews = () => {\n  _s();\n  const [news, setNews] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  useEffect(() => {\n    fetchNews();\n  }, []);\n  const fetchNews = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.admin.getNews();\n      setNews(response.data);\n    } catch (error) {\n      setError('Failed to load news');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const columns = [{\n    key: 'image_url',\n    label: 'Image',\n    type: 'image'\n  }, {\n    key: 'title',\n    label: 'Title',\n    sortable: true\n  }, {\n    key: 'author',\n    label: 'Author',\n    sortable: true\n  }, {\n    key: 'is_published',\n    label: 'Published',\n    type: 'boolean'\n  }, {\n    key: 'published_at',\n    label: 'Date',\n    type: 'date',\n    sortable: true\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-800 mb-2\",\n        children: \"News & Updates Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Manage news articles and company updates\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n      message: error,\n      onClose: () => setError(null),\n      className: \"mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(SuccessMessage, {\n      message: success,\n      onClose: () => setSuccess(null),\n      className: \"mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(CrudTable, {\n      title: \"News Articles\",\n      data: news,\n      columns: columns,\n      loading: loading,\n      addButtonText: \"Add Article\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminNews, \"NWf2NAx1r7qbwNrmYd8alP83BfM=\");\n_c = AdminNews;\nexport default AdminNews;\nvar _c;\n$RefreshReg$(_c, \"AdminNews\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "apiService", "CrudTable", "LoadingSpinner", "ErrorMessage", "SuccessMessage", "jsxDEV", "_jsxDEV", "AdminNews", "_s", "news", "setNews", "loading", "setLoading", "error", "setError", "success", "setSuccess", "fetchNews", "response", "admin", "getNews", "data", "columns", "key", "label", "type", "sortable", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "onClose", "title", "addButtonText", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/admin/AdminNews.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport apiService from '../services/api';\nimport CrudTable from './components/CrudTable';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport SuccessMessage from '../components/SuccessMessage';\n\nconst AdminNews = () => {\n  const [news, setNews] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  useEffect(() => {\n    fetchNews();\n  }, []);\n\n  const fetchNews = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.admin.getNews();\n      setNews(response.data);\n    } catch (error) {\n      setError('Failed to load news');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const columns = [\n    { key: 'image_url', label: 'Image', type: 'image' },\n    { key: 'title', label: 'Title', sortable: true },\n    { key: 'author', label: 'Author', sortable: true },\n    { key: 'is_published', label: 'Published', type: 'boolean' },\n    { key: 'published_at', label: 'Date', type: 'date', sortable: true }\n  ];\n\n  return (\n    <div>\n      <div className=\"mb-6\">\n        <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">News & Updates Management</h1>\n        <p className=\"text-gray-600\">Manage news articles and company updates</p>\n      </div>\n\n      {error && <ErrorMessage message={error} onClose={() => setError(null)} className=\"mb-4\" />}\n      {success && <SuccessMessage message={success} onClose={() => setSuccess(null)} className=\"mb-4\" />}\n\n      <CrudTable\n        title=\"News Articles\"\n        data={news}\n        columns={columns}\n        loading={loading}\n        addButtonText=\"Add Article\"\n      />\n    </div>\n  );\n};\n\nexport default AdminNews;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdkB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAMlB,UAAU,CAACmB,KAAK,CAACC,OAAO,CAAC,CAAC;MACjDV,OAAO,CAACQ,QAAQ,CAACG,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,OAAO,GAAG,CACd;IAAEC,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAQ,CAAC,EACnD;IAAEF,GAAG,EAAE,OAAO;IAAEC,KAAK,EAAE,OAAO;IAAEE,QAAQ,EAAE;EAAK,CAAC,EAChD;IAAEH,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEE,QAAQ,EAAE;EAAK,CAAC,EAClD;IAAEH,GAAG,EAAE,cAAc;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAU,CAAC,EAC5D;IAAEF,GAAG,EAAE,cAAc;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,MAAM;IAAEC,QAAQ,EAAE;EAAK,CAAC,CACrE;EAED,oBACEpB,OAAA;IAAAqB,QAAA,gBACErB,OAAA;MAAKsB,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBrB,OAAA;QAAIsB,SAAS,EAAC,uCAAuC;QAAAD,QAAA,EAAC;MAAyB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpF1B,OAAA;QAAGsB,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAC;MAAwC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,EAELnB,KAAK,iBAAIP,OAAA,CAACH,YAAY;MAAC8B,OAAO,EAAEpB,KAAM;MAACqB,OAAO,EAAEA,CAAA,KAAMpB,QAAQ,CAAC,IAAI,CAAE;MAACc,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACzFjB,OAAO,iBAAIT,OAAA,CAACF,cAAc;MAAC6B,OAAO,EAAElB,OAAQ;MAACmB,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,IAAI,CAAE;MAACY,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAElG1B,OAAA,CAACL,SAAS;MACRkC,KAAK,EAAC,eAAe;MACrBd,IAAI,EAAEZ,IAAK;MACXa,OAAO,EAAEA,OAAQ;MACjBX,OAAO,EAAEA,OAAQ;MACjByB,aAAa,EAAC;IAAa;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxB,EAAA,CAjDID,SAAS;AAAA8B,EAAA,GAAT9B,SAAS;AAmDf,eAAeA,SAAS;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}