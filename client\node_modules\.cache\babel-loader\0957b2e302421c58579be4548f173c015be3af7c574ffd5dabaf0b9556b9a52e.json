{"ast": null, "code": "import { appearStoreId } from './store-id.mjs';\nimport { animateStyle } from '../animators/waapi/index.mjs';\nimport { optimizedAppearDataId } from './data-id.mjs';\nimport { handoffOptimizedAppearAnimation } from './handoff.mjs';\nimport { appearAnimationStore } from './store.mjs';\nimport { noop } from '../../utils/noop.mjs';\n\n/**\n * A single time to use across all animations to manually set startTime\n * and ensure they're all in sync.\n */\nlet startFrameTime;\n/**\n * A dummy animation to detect when Chrome is ready to start\n * painting the page and hold off from triggering the real animation\n * until then. We only need one animation to detect paint ready.\n *\n * https://bugs.chromium.org/p/chromium/issues/detail?id=1406850\n */\nlet readyAnimation;\nfunction startOptimizedAppearAnimation(element, name, keyframes, options, onReady) {\n  // Prevent optimised appear animations if Motion has already started animating.\n  if (window.HandoffComplete) {\n    window.HandoffAppearAnimations = undefined;\n    return;\n  }\n  const id = element.dataset[optimizedAppearDataId];\n  if (!id) return;\n  window.HandoffAppearAnimations = handoffOptimizedAppearAnimation;\n  const storeId = appearStoreId(id, name);\n  if (!readyAnimation) {\n    readyAnimation = animateStyle(element, name, [keyframes[0], keyframes[0]],\n    /**\n     * 10 secs is basically just a super-safe duration to give Chrome\n     * long enough to get the animation ready.\n     */\n    {\n      duration: 10000,\n      ease: \"linear\"\n    });\n    appearAnimationStore.set(storeId, {\n      animation: readyAnimation,\n      startTime: null\n    });\n  }\n  const startAnimation = () => {\n    readyAnimation.cancel();\n    const appearAnimation = animateStyle(element, name, keyframes, options);\n    /**\n     * Record the time of the first started animation. We call performance.now() once\n     * here and once in handoff to ensure we're getting\n     * close to a frame-locked time. This keeps all animations in sync.\n     */\n    if (startFrameTime === undefined) {\n      startFrameTime = performance.now();\n    }\n    appearAnimation.startTime = startFrameTime;\n    appearAnimationStore.set(storeId, {\n      animation: appearAnimation,\n      startTime: startFrameTime\n    });\n    if (onReady) onReady(appearAnimation);\n  };\n  if (readyAnimation.ready) {\n    readyAnimation.ready.then(startAnimation).catch(noop);\n  } else {\n    startAnimation();\n  }\n}\nexport { startOptimizedAppearAnimation };", "map": {"version": 3, "names": ["appearStoreId", "animateStyle", "optimizedAppearDataId", "handoffOptimizedAppearAnimation", "appearAnimationStore", "noop", "startFrameTime", "readyAnimation", "startOptimizedAppearAnimation", "element", "name", "keyframes", "options", "onReady", "window", "HandoffComplete", "HandoffAppearAnimations", "undefined", "id", "dataset", "storeId", "duration", "ease", "set", "animation", "startTime", "startAnimation", "cancel", "appearAnimation", "performance", "now", "ready", "then", "catch"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/node_modules/framer-motion/dist/es/animation/optimized-appear/start.mjs"], "sourcesContent": ["import { appearStoreId } from './store-id.mjs';\nimport { animateStyle } from '../animators/waapi/index.mjs';\nimport { optimizedAppearDataId } from './data-id.mjs';\nimport { handoffOptimizedAppearAnimation } from './handoff.mjs';\nimport { appearAnimationStore } from './store.mjs';\nimport { noop } from '../../utils/noop.mjs';\n\n/**\n * A single time to use across all animations to manually set startTime\n * and ensure they're all in sync.\n */\nlet startFrameTime;\n/**\n * A dummy animation to detect when Chrome is ready to start\n * painting the page and hold off from triggering the real animation\n * until then. We only need one animation to detect paint ready.\n *\n * https://bugs.chromium.org/p/chromium/issues/detail?id=1406850\n */\nlet readyAnimation;\nfunction startOptimizedAppearAnimation(element, name, keyframes, options, onReady) {\n    // Prevent optimised appear animations if Motion has already started animating.\n    if (window.HandoffComplete) {\n        window.HandoffAppearAnimations = undefined;\n        return;\n    }\n    const id = element.dataset[optimizedAppearDataId];\n    if (!id)\n        return;\n    window.HandoffAppearAnimations = handoffOptimizedAppearAnimation;\n    const storeId = appearStoreId(id, name);\n    if (!readyAnimation) {\n        readyAnimation = animateStyle(element, name, [keyframes[0], keyframes[0]], \n        /**\n         * 10 secs is basically just a super-safe duration to give Chrome\n         * long enough to get the animation ready.\n         */\n        { duration: 10000, ease: \"linear\" });\n        appearAnimationStore.set(storeId, {\n            animation: readyAnimation,\n            startTime: null,\n        });\n    }\n    const startAnimation = () => {\n        readyAnimation.cancel();\n        const appearAnimation = animateStyle(element, name, keyframes, options);\n        /**\n         * Record the time of the first started animation. We call performance.now() once\n         * here and once in handoff to ensure we're getting\n         * close to a frame-locked time. This keeps all animations in sync.\n         */\n        if (startFrameTime === undefined) {\n            startFrameTime = performance.now();\n        }\n        appearAnimation.startTime = startFrameTime;\n        appearAnimationStore.set(storeId, {\n            animation: appearAnimation,\n            startTime: startFrameTime,\n        });\n        if (onReady)\n            onReady(appearAnimation);\n    };\n    if (readyAnimation.ready) {\n        readyAnimation.ready.then(startAnimation).catch(noop);\n    }\n    else {\n        startAnimation();\n    }\n}\n\nexport { startOptimizedAppearAnimation };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,qBAAqB,QAAQ,eAAe;AACrD,SAASC,+BAA+B,QAAQ,eAAe;AAC/D,SAASC,oBAAoB,QAAQ,aAAa;AAClD,SAASC,IAAI,QAAQ,sBAAsB;;AAE3C;AACA;AACA;AACA;AACA,IAAIC,cAAc;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc;AAClB,SAASC,6BAA6BA,CAACC,OAAO,EAAEC,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC/E;EACA,IAAIC,MAAM,CAACC,eAAe,EAAE;IACxBD,MAAM,CAACE,uBAAuB,GAAGC,SAAS;IAC1C;EACJ;EACA,MAAMC,EAAE,GAAGT,OAAO,CAACU,OAAO,CAACjB,qBAAqB,CAAC;EACjD,IAAI,CAACgB,EAAE,EACH;EACJJ,MAAM,CAACE,uBAAuB,GAAGb,+BAA+B;EAChE,MAAMiB,OAAO,GAAGpB,aAAa,CAACkB,EAAE,EAAER,IAAI,CAAC;EACvC,IAAI,CAACH,cAAc,EAAE;IACjBA,cAAc,GAAGN,YAAY,CAACQ,OAAO,EAAEC,IAAI,EAAE,CAACC,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;IACzE;AACR;AACA;AACA;IACQ;MAAEU,QAAQ,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAS,CAAC,CAAC;IACpClB,oBAAoB,CAACmB,GAAG,CAACH,OAAO,EAAE;MAC9BI,SAAS,EAAEjB,cAAc;MACzBkB,SAAS,EAAE;IACf,CAAC,CAAC;EACN;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IACzBnB,cAAc,CAACoB,MAAM,CAAC,CAAC;IACvB,MAAMC,eAAe,GAAG3B,YAAY,CAACQ,OAAO,EAAEC,IAAI,EAAEC,SAAS,EAAEC,OAAO,CAAC;IACvE;AACR;AACA;AACA;AACA;IACQ,IAAIN,cAAc,KAAKW,SAAS,EAAE;MAC9BX,cAAc,GAAGuB,WAAW,CAACC,GAAG,CAAC,CAAC;IACtC;IACAF,eAAe,CAACH,SAAS,GAAGnB,cAAc;IAC1CF,oBAAoB,CAACmB,GAAG,CAACH,OAAO,EAAE;MAC9BI,SAAS,EAAEI,eAAe;MAC1BH,SAAS,EAAEnB;IACf,CAAC,CAAC;IACF,IAAIO,OAAO,EACPA,OAAO,CAACe,eAAe,CAAC;EAChC,CAAC;EACD,IAAIrB,cAAc,CAACwB,KAAK,EAAE;IACtBxB,cAAc,CAACwB,KAAK,CAACC,IAAI,CAACN,cAAc,CAAC,CAACO,KAAK,CAAC5B,IAAI,CAAC;EACzD,CAAC,MACI;IACDqB,cAAc,CAAC,CAAC;EACpB;AACJ;AAEA,SAASlB,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}