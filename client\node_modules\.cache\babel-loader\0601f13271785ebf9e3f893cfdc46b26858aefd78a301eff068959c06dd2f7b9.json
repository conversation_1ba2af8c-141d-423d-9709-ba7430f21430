{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\admin\\\\AdminClients.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport apiService from '../services/api';\nimport CrudTable from './components/CrudTable';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport SuccessMessage from '../components/SuccessMessage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminClients = () => {\n  _s();\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  useEffect(() => {\n    fetchClients();\n  }, []);\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.admin.getClients();\n      setClients(response.data);\n    } catch (error) {\n      setError('Failed to load clients');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const columns = [{\n    key: 'logo_url',\n    label: 'Logo',\n    type: 'image'\n  }, {\n    key: 'name',\n    label: 'Client Name',\n    sortable: true\n  }, {\n    key: 'partnership_year',\n    label: 'Partnership Year',\n    sortable: true\n  }, {\n    key: 'is_featured',\n    label: 'Featured',\n    type: 'boolean'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-800 mb-2\",\n        children: \"Corporate Clients Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Manage your corporate client portfolio\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n      message: error,\n      onClose: () => setError(null),\n      className: \"mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(SuccessMessage, {\n      message: success,\n      onClose: () => setSuccess(null),\n      className: \"mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(CrudTable, {\n      title: \"Corporate Clients\",\n      data: clients,\n      columns: columns,\n      loading: loading,\n      addButtonText: \"Add Client\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminClients, \"+pevezc+dSEKuND3TEyo+ABBN7k=\");\n_c = AdminClients;\nexport default AdminClients;\nvar _c;\n$RefreshReg$(_c, \"AdminClients\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "apiService", "CrudTable", "LoadingSpinner", "ErrorMessage", "SuccessMessage", "jsxDEV", "_jsxDEV", "AdminClients", "_s", "clients", "setClients", "loading", "setLoading", "error", "setError", "success", "setSuccess", "fetchClients", "response", "admin", "getClients", "data", "columns", "key", "label", "type", "sortable", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "onClose", "title", "addButtonText", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/admin/AdminClients.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport apiService from '../services/api';\nimport CrudTable from './components/CrudTable';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport SuccessMessage from '../components/SuccessMessage';\n\nconst AdminClients = () => {\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  useEffect(() => {\n    fetchClients();\n  }, []);\n\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.admin.getClients();\n      setClients(response.data);\n    } catch (error) {\n      setError('Failed to load clients');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const columns = [\n    { key: 'logo_url', label: 'Logo', type: 'image' },\n    { key: 'name', label: 'Client Name', sortable: true },\n    { key: 'partnership_year', label: 'Partnership Year', sortable: true },\n    { key: 'is_featured', label: 'Featured', type: 'boolean' }\n  ];\n\n  return (\n    <div>\n      <div className=\"mb-6\">\n        <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">Corporate Clients Management</h1>\n        <p className=\"text-gray-600\">Manage your corporate client portfolio</p>\n      </div>\n\n      {error && <ErrorMessage message={error} onClose={() => setError(null)} className=\"mb-4\" />}\n      {success && <SuccessMessage message={success} onClose={() => setSuccess(null)} className=\"mb-4\" />}\n\n      <CrudTable\n        title=\"Corporate Clients\"\n        data={clients}\n        columns={columns}\n        loading={loading}\n        addButtonText=\"Add Client\"\n      />\n    </div>\n  );\n};\n\nexport default AdminClients;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdkB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAMlB,UAAU,CAACmB,KAAK,CAACC,UAAU,CAAC,CAAC;MACpDV,UAAU,CAACQ,QAAQ,CAACG,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,QAAQ,CAAC,wBAAwB,CAAC;IACpC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,OAAO,GAAG,CACd;IAAEC,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAQ,CAAC,EACjD;IAAEF,GAAG,EAAE,MAAM;IAAEC,KAAK,EAAE,aAAa;IAAEE,QAAQ,EAAE;EAAK,CAAC,EACrD;IAAEH,GAAG,EAAE,kBAAkB;IAAEC,KAAK,EAAE,kBAAkB;IAAEE,QAAQ,EAAE;EAAK,CAAC,EACtE;IAAEH,GAAG,EAAE,aAAa;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAU,CAAC,CAC3D;EAED,oBACEnB,OAAA;IAAAqB,QAAA,gBACErB,OAAA;MAAKsB,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBrB,OAAA;QAAIsB,SAAS,EAAC,uCAAuC;QAAAD,QAAA,EAAC;MAA4B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvF1B,OAAA;QAAGsB,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC,EAELnB,KAAK,iBAAIP,OAAA,CAACH,YAAY;MAAC8B,OAAO,EAAEpB,KAAM;MAACqB,OAAO,EAAEA,CAAA,KAAMpB,QAAQ,CAAC,IAAI,CAAE;MAACc,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACzFjB,OAAO,iBAAIT,OAAA,CAACF,cAAc;MAAC6B,OAAO,EAAElB,OAAQ;MAACmB,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,IAAI,CAAE;MAACY,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAElG1B,OAAA,CAACL,SAAS;MACRkC,KAAK,EAAC,mBAAmB;MACzBd,IAAI,EAAEZ,OAAQ;MACda,OAAO,EAAEA,OAAQ;MACjBX,OAAO,EAAEA,OAAQ;MACjByB,aAAa,EAAC;IAAY;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxB,EAAA,CAhDID,YAAY;AAAA8B,EAAA,GAAZ9B,YAAY;AAkDlB,eAAeA,YAAY;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}