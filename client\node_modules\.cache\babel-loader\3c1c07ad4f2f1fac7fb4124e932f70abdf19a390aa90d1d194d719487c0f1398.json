{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\admin\\\\AdminTeam.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport apiService from '../services/api';\nimport CrudTable from './components/CrudTable';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport SuccessMessage from '../components/SuccessMessage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminTeam = () => {\n  _s();\n  const [team, setTeam] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  useEffect(() => {\n    fetchTeam();\n  }, []);\n  const fetchTeam = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.admin.getTeam();\n      setTeam(response.data);\n    } catch (error) {\n      setError('Failed to load team members');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const columns = [{\n    key: 'photo_url',\n    label: 'Photo',\n    type: 'image'\n  }, {\n    key: 'name',\n    label: 'Name',\n    sortable: true\n  }, {\n    key: 'position',\n    label: 'Position',\n    sortable: true\n  }, {\n    key: 'email',\n    label: 'Email'\n  }, {\n    key: 'is_active',\n    label: 'Status',\n    type: 'boolean'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-800 mb-2\",\n        children: \"Team Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Manage team member profiles\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n      message: error,\n      onClose: () => setError(null),\n      className: \"mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(SuccessMessage, {\n      message: success,\n      onClose: () => setSuccess(null),\n      className: \"mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(CrudTable, {\n      title: \"Team Members\",\n      data: team,\n      columns: columns,\n      loading: loading,\n      addButtonText: \"Add Team Member\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminTeam, \"c73kH8FXwME1A55+rg5hUYFdwoM=\");\n_c = AdminTeam;\nexport default AdminTeam;\nvar _c;\n$RefreshReg$(_c, \"AdminTeam\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "apiService", "CrudTable", "LoadingSpinner", "ErrorMessage", "SuccessMessage", "jsxDEV", "_jsxDEV", "AdminTeam", "_s", "team", "setTeam", "loading", "setLoading", "error", "setError", "success", "setSuccess", "fetchTeam", "response", "admin", "getTeam", "data", "columns", "key", "label", "type", "sortable", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "onClose", "title", "addButtonText", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/admin/AdminTeam.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport apiService from '../services/api';\nimport CrudTable from './components/CrudTable';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport SuccessMessage from '../components/SuccessMessage';\n\nconst AdminTeam = () => {\n  const [team, setTeam] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  useEffect(() => {\n    fetchTeam();\n  }, []);\n\n  const fetchTeam = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.admin.getTeam();\n      setTeam(response.data);\n    } catch (error) {\n      setError('Failed to load team members');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const columns = [\n    { key: 'photo_url', label: 'Photo', type: 'image' },\n    { key: 'name', label: 'Name', sortable: true },\n    { key: 'position', label: 'Position', sortable: true },\n    { key: 'email', label: 'Email' },\n    { key: 'is_active', label: 'Status', type: 'boolean' }\n  ];\n\n  return (\n    <div>\n      <div className=\"mb-6\">\n        <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">Team Management</h1>\n        <p className=\"text-gray-600\">Manage team member profiles</p>\n      </div>\n\n      {error && <ErrorMessage message={error} onClose={() => setError(null)} className=\"mb-4\" />}\n      {success && <SuccessMessage message={success} onClose={() => setSuccess(null)} className=\"mb-4\" />}\n\n      <CrudTable\n        title=\"Team Members\"\n        data={team}\n        columns={columns}\n        loading={loading}\n        addButtonText=\"Add Team Member\"\n      />\n    </div>\n  );\n};\n\nexport default AdminTeam;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdkB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAMlB,UAAU,CAACmB,KAAK,CAACC,OAAO,CAAC,CAAC;MACjDV,OAAO,CAACQ,QAAQ,CAACG,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,QAAQ,CAAC,6BAA6B,CAAC;IACzC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,OAAO,GAAG,CACd;IAAEC,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAQ,CAAC,EACnD;IAAEF,GAAG,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEE,QAAQ,EAAE;EAAK,CAAC,EAC9C;IAAEH,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEE,QAAQ,EAAE;EAAK,CAAC,EACtD;IAAEH,GAAG,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAChC;IAAED,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAU,CAAC,CACvD;EAED,oBACEnB,OAAA;IAAAqB,QAAA,gBACErB,OAAA;MAAKsB,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBrB,OAAA;QAAIsB,SAAS,EAAC,uCAAuC;QAAAD,QAAA,EAAC;MAAe;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1E1B,OAAA;QAAGsB,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAC;MAA2B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,EAELnB,KAAK,iBAAIP,OAAA,CAACH,YAAY;MAAC8B,OAAO,EAAEpB,KAAM;MAACqB,OAAO,EAAEA,CAAA,KAAMpB,QAAQ,CAAC,IAAI,CAAE;MAACc,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACzFjB,OAAO,iBAAIT,OAAA,CAACF,cAAc;MAAC6B,OAAO,EAAElB,OAAQ;MAACmB,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,IAAI,CAAE;MAACY,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAElG1B,OAAA,CAACL,SAAS;MACRkC,KAAK,EAAC,cAAc;MACpBd,IAAI,EAAEZ,IAAK;MACXa,OAAO,EAAEA,OAAQ;MACjBX,OAAO,EAAEA,OAAQ;MACjByB,aAAa,EAAC;IAAiB;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxB,EAAA,CAjDID,SAAS;AAAA8B,EAAA,GAAT9B,SAAS;AAmDf,eAAeA,SAAS;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}