{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\components\\\\LoadingSpinner.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  size = 'md',\n  className = ''\n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n    xl: 'w-16 h-16'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex items-center justify-center ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${sizeClasses[size]} border-4 border-gray-200 border-t-primary-green rounded-full animate-spin`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LoadingSpinner", "size", "className", "sizeClasses", "sm", "md", "lg", "xl", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/components/LoadingSpinner.js"], "sourcesContent": ["import React from 'react';\n\nconst LoadingSpinner = ({ size = 'md', className = '' }) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n    xl: 'w-16 h-16'\n  };\n\n  return (\n    <div className={`flex items-center justify-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-primary-green rounded-full animate-spin`}></div>\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI,GAAG,IAAI;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAC1D,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE;EACN,CAAC;EAED,oBACER,OAAA;IAAKG,SAAS,EAAE,oCAAoCA,SAAS,EAAG;IAAAM,QAAA,eAC9DT,OAAA;MAAKG,SAAS,EAAE,GAAGC,WAAW,CAACF,IAAI,CAAC;IAA6E;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrH,CAAC;AAEV,CAAC;AAACC,EAAA,GAbIb,cAAc;AAepB,eAAeA,cAAc;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}