{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { FaArrowRight, FaUsers, FaHandshake, FaAward, FaShieldAlt } from 'react-icons/fa';\nimport { useSiteData } from '../contexts/SiteDataContext';\nimport apiService from '../services/api';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const {\n    siteInfo\n  } = useSiteData();\n  const [featuredClients, setFeaturedClients] = useState([]);\n  const [services, setServices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchHomeData();\n  }, []);\n  const fetchHomeData = async () => {\n    try {\n      const [clientsResponse, servicesResponse] = await Promise.all([apiService.public.getFeaturedClients(), apiService.public.getServices()]);\n      setFeaturedClients(clientsResponse.data);\n      setServices(servicesResponse.data.slice(0, 3)); // Show only first 3 services\n    } catch (error) {\n      console.error('Error fetching home data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const {\n    settings,\n    contact\n  } = siteInfo;\n  const highlights = [{\n    icon: FaAward,\n    label: 'Years of Excellence',\n    value: settings !== null && settings !== void 0 && settings.company_established ? `${new Date().getFullYear() - parseInt(settings.company_established)}+` : '15+',\n    description: 'Years serving the pharmaceutical industry'\n  }, {\n    icon: FaUsers,\n    label: 'Corporate Clients',\n    value: (settings === null || settings === void 0 ? void 0 : settings.total_clients) || '150+',\n    description: 'Trusted corporate partnerships'\n  }, {\n    icon: FaHandshake,\n    label: 'Trusted Suppliers',\n    value: (settings === null || settings === void 0 ? void 0 : settings.total_suppliers) || '50+',\n    description: 'Global pharmaceutical suppliers'\n  }, {\n    icon: FaShieldAlt,\n    label: 'Quality Assurance',\n    value: '100%',\n    description: 'Commitment to pharmaceutical standards'\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        size: \"xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pt-20\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"bg-gradient-to-br from-primary-green to-primary-green-dark text-white py-20 lg:py-32\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto text-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8\n            },\n            className: \"text-4xl lg:text-6xl font-bold mb-6\",\n            children: (settings === null || settings === void 0 ? void 0 : settings.hero_title) || 'Pharmaceutical Distribution Excellence'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            className: \"text-xl lg:text-2xl text-secondary-green mb-8 leading-relaxed\",\n            children: (settings === null || settings === void 0 ? void 0 : settings.hero_subtitle) || 'Your trusted partner in wholesale pharmaceutical distribution, serving corporate clients with quality and reliability.'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.4\n            },\n            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/about\",\n              className: \"btn btn-white\",\n              children: [\"Learn About Us\", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n                className: \"ml-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/services\",\n              className: \"btn btn-outline\",\n              children: \"Our Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 lg:py-24 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-3xl mx-auto text-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"text-3xl lg:text-4xl font-bold text-primary-green mb-6\",\n            children: \"Leading Pharmaceutical Distribution\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            viewport: {\n              once: true\n            },\n            className: \"text-lg text-gray-600 leading-relaxed\",\n            children: \"Al-Fayasel Drugstore has been at the forefront of pharmaceutical distribution, connecting healthcare providers with essential medicines through our extensive network of trusted suppliers and commitment to excellence.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 lg:py-24 bg-background-grey\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-3xl lg:text-4xl font-bold text-center text-primary-green mb-12\",\n          children: \"Why Choose Al-Fayasel?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: highlights.map((highlight, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"card text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-secondary-green rounded-full flex items-center justify-center mx-auto mb-4\",\n              children: /*#__PURE__*/_jsxDEV(highlight.icon, {\n                className: \"text-2xl text-primary-green\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-3xl font-bold text-primary-green mb-2\",\n              children: highlight.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-gray-800 mb-2\",\n              children: highlight.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: highlight.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), services.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 lg:py-24 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"text-3xl lg:text-4xl font-bold text-primary-green mb-4\",\n            children: \"Our Core Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            viewport: {\n              once: true\n            },\n            className: \"text-lg text-gray-600\",\n            children: \"Comprehensive pharmaceutical distribution solutions for corporate clients\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\",\n          children: services.map((service, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"card text-center\",\n            children: [service.image_url && /*#__PURE__*/_jsxDEV(\"img\", {\n              src: service.image_url,\n              alt: service.name,\n              className: \"w-16 h-16 mx-auto mb-4 object-contain\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-primary-green mb-3\",\n              children: service.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: service.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 19\n            }, this)]\n          }, service.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/services\",\n            className: \"btn btn-primary\",\n            children: [\"View All Services\", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n              className: \"ml-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 9\n    }, this), featuredClients.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 lg:py-24 bg-background-grey\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-3xl lg:text-4xl font-bold text-center text-primary-green mb-12\",\n          children: \"Trusted by Leading Organizations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8\",\n          children: featuredClients.map((client, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-lg p-6 flex items-center justify-center hover:shadow-lg transition-shadow duration-300\",\n            children: client.logo_url ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: client.logo_url,\n              alt: client.name,\n              className: \"max-w-full max-h-16 object-contain filter grayscale hover:grayscale-0 transition-all duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-500 font-medium text-sm text-center\",\n              children: client.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 21\n            }, this)\n          }, client.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mt-12\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/clients\",\n            className: \"btn btn-primary\",\n            children: [\"View All Clients\", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n              className: \"ml-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 lg:py-24 bg-primary-green text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-3xl lg:text-4xl font-bold mb-6\",\n          children: \"Ready to Partner with Us?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-xl text-secondary-green mb-8 max-w-2xl mx-auto\",\n          children: \"Join our network of satisfied corporate clients and experience the difference of working with a trusted pharmaceutical distributor.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.4\n          },\n          viewport: {\n            once: true\n          },\n          className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            className: \"btn btn-white\",\n            children: [\"Get in Touch\", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n              className: \"ml-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"btn btn-outline\",\n            children: \"Browse Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"OmfC1SactzwA+kRGUDviP2ppZtQ=\", false, function () {\n  return [useSiteData];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "motion", "FaArrowRight", "FaUsers", "FaHandshake", "FaAward", "FaShieldAlt", "useSiteData", "apiService", "LoadingSpinner", "jsxDEV", "_jsxDEV", "Home", "_s", "siteInfo", "featuredClients", "setFeaturedClients", "services", "setServices", "loading", "setLoading", "fetchHomeData", "clientsResponse", "servicesResponse", "Promise", "all", "public", "getFeaturedClients", "getServices", "data", "slice", "error", "console", "settings", "contact", "highlights", "icon", "label", "value", "company_established", "Date", "getFullYear", "parseInt", "description", "total_clients", "total_suppliers", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "h1", "initial", "opacity", "y", "animate", "transition", "duration", "hero_title", "p", "delay", "hero_subtitle", "div", "to", "h2", "whileInView", "viewport", "once", "map", "highlight", "index", "length", "service", "image_url", "src", "alt", "name", "id", "client", "scale", "logo_url", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/pages/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { FaArrowRight, FaUsers, FaHandshake, FaAward, FaShieldAlt } from 'react-icons/fa';\nimport { useSiteData } from '../contexts/SiteDataContext';\nimport apiService from '../services/api';\nimport LoadingSpinner from '../components/LoadingSpinner';\n\nconst Home = () => {\n  const { siteInfo } = useSiteData();\n  const [featuredClients, setFeaturedClients] = useState([]);\n  const [services, setServices] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchHomeData();\n  }, []);\n\n  const fetchHomeData = async () => {\n    try {\n      const [clientsResponse, servicesResponse] = await Promise.all([\n        apiService.public.getFeaturedClients(),\n        apiService.public.getServices()\n      ]);\n      \n      setFeaturedClients(clientsResponse.data);\n      setServices(servicesResponse.data.slice(0, 3)); // Show only first 3 services\n    } catch (error) {\n      console.error('Error fetching home data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const { settings, contact } = siteInfo;\n\n  const highlights = [\n    {\n      icon: FaAward,\n      label: 'Years of Excellence',\n      value: settings?.company_established ? `${new Date().getFullYear() - parseInt(settings.company_established)}+` : '15+',\n      description: 'Years serving the pharmaceutical industry'\n    },\n    {\n      icon: FaUsers,\n      label: 'Corporate Clients',\n      value: settings?.total_clients || '150+',\n      description: 'Trusted corporate partnerships'\n    },\n    {\n      icon: FaHandshake,\n      label: 'Trusted Suppliers',\n      value: settings?.total_suppliers || '50+',\n      description: 'Global pharmaceutical suppliers'\n    },\n    {\n      icon: FaShieldAlt,\n      label: 'Quality Assurance',\n      value: '100%',\n      description: 'Commitment to pharmaceutical standards'\n    }\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"xl\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"pt-20\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-primary-green to-primary-green-dark text-white py-20 lg:py-32\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <motion.h1\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"text-4xl lg:text-6xl font-bold mb-6\"\n            >\n              {settings?.hero_title || 'Pharmaceutical Distribution Excellence'}\n            </motion.h1>\n            \n            <motion.p\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"text-xl lg:text-2xl text-secondary-green mb-8 leading-relaxed\"\n            >\n              {settings?.hero_subtitle || 'Your trusted partner in wholesale pharmaceutical distribution, serving corporate clients with quality and reliability.'}\n            </motion.p>\n            \n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n            >\n              <Link to=\"/about\" className=\"btn btn-white\">\n                Learn About Us\n                <FaArrowRight className=\"ml-2\" />\n              </Link>\n              <Link to=\"/services\" className=\"btn btn-outline\">\n                Our Services\n              </Link>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Company Overview */}\n      <section className=\"py-16 lg:py-24 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-3xl mx-auto text-center\">\n            <motion.h2\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-3xl lg:text-4xl font-bold text-primary-green mb-6\"\n            >\n              Leading Pharmaceutical Distribution\n            </motion.h2>\n            \n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n              className=\"text-lg text-gray-600 leading-relaxed\"\n            >\n              Al-Fayasel Drugstore has been at the forefront of pharmaceutical distribution, \n              connecting healthcare providers with essential medicines through our extensive \n              network of trusted suppliers and commitment to excellence.\n            </motion.p>\n          </div>\n        </div>\n      </section>\n\n      {/* Highlights Section */}\n      <section className=\"py-16 lg:py-24 bg-background-grey\">\n        <div className=\"container mx-auto px-4\">\n          <motion.h2\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-3xl lg:text-4xl font-bold text-center text-primary-green mb-12\"\n          >\n            Why Choose Al-Fayasel?\n          </motion.h2>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {highlights.map((highlight, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"card text-center\"\n              >\n                <div className=\"w-16 h-16 bg-secondary-green rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <highlight.icon className=\"text-2xl text-primary-green\" />\n                </div>\n                <h3 className=\"text-3xl font-bold text-primary-green mb-2\">\n                  {highlight.value}\n                </h3>\n                <h4 className=\"text-lg font-semibold text-gray-800 mb-2\">\n                  {highlight.label}\n                </h4>\n                <p className=\"text-gray-600 text-sm\">\n                  {highlight.description}\n                </p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Services Preview */}\n      {services.length > 0 && (\n        <section className=\"py-16 lg:py-24 bg-white\">\n          <div className=\"container mx-auto px-4\">\n            <div className=\"text-center mb-12\">\n              <motion.h2\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6 }}\n                viewport={{ once: true }}\n                className=\"text-3xl lg:text-4xl font-bold text-primary-green mb-4\"\n              >\n                Our Core Services\n              </motion.h2>\n              <motion.p\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                viewport={{ once: true }}\n                className=\"text-lg text-gray-600\"\n              >\n                Comprehensive pharmaceutical distribution solutions for corporate clients\n              </motion.p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n              {services.map((service, index) => (\n                <motion.div\n                  key={service.id}\n                  initial={{ opacity: 0, y: 30 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"card text-center\"\n                >\n                  {service.image_url && (\n                    <img\n                      src={service.image_url}\n                      alt={service.name}\n                      className=\"w-16 h-16 mx-auto mb-4 object-contain\"\n                    />\n                  )}\n                  <h3 className=\"text-xl font-semibold text-primary-green mb-3\">\n                    {service.name}\n                  </h3>\n                  <p className=\"text-gray-600\">\n                    {service.description}\n                  </p>\n                </motion.div>\n              ))}\n            </div>\n            \n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-center\"\n            >\n              <Link to=\"/services\" className=\"btn btn-primary\">\n                View All Services\n                <FaArrowRight className=\"ml-2\" />\n              </Link>\n            </motion.div>\n          </div>\n        </section>\n      )}\n\n      {/* Featured Clients */}\n      {featuredClients.length > 0 && (\n        <section className=\"py-16 lg:py-24 bg-background-grey\">\n          <div className=\"container mx-auto px-4\">\n            <motion.h2\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-3xl lg:text-4xl font-bold text-center text-primary-green mb-12\"\n            >\n              Trusted by Leading Organizations\n            </motion.h2>\n            \n            <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8\">\n              {featuredClients.map((client, index) => (\n                <motion.div\n                  key={client.id}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"bg-white rounded-lg p-6 flex items-center justify-center hover:shadow-lg transition-shadow duration-300\"\n                >\n                  {client.logo_url ? (\n                    <img\n                      src={client.logo_url}\n                      alt={client.name}\n                      className=\"max-w-full max-h-16 object-contain filter grayscale hover:grayscale-0 transition-all duration-300\"\n                    />\n                  ) : (\n                    <span className=\"text-gray-500 font-medium text-sm text-center\">\n                      {client.name}\n                    </span>\n                  )}\n                </motion.div>\n              ))}\n            </div>\n            \n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-center mt-12\"\n            >\n              <Link to=\"/clients\" className=\"btn btn-primary\">\n                View All Clients\n                <FaArrowRight className=\"ml-2\" />\n              </Link>\n            </motion.div>\n          </div>\n        </section>\n      )}\n\n      {/* Call to Action */}\n      <section className=\"py-16 lg:py-24 bg-primary-green text-white\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <motion.h2\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-3xl lg:text-4xl font-bold mb-6\"\n          >\n            Ready to Partner with Us?\n          </motion.h2>\n          \n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"text-xl text-secondary-green mb-8 max-w-2xl mx-auto\"\n          >\n            Join our network of satisfied corporate clients and experience \n            the difference of working with a trusted pharmaceutical distributor.\n          </motion.p>\n          \n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            viewport={{ once: true }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n          >\n            <Link to=\"/contact\" className=\"btn btn-white\">\n              Get in Touch\n              <FaArrowRight className=\"ml-2\" />\n            </Link>\n            <Link to=\"/products\" className=\"btn btn-outline\">\n              Browse Products\n            </Link>\n          </motion.div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,YAAY,EAAEC,OAAO,EAAEC,WAAW,EAAEC,OAAO,EAAEC,WAAW,QAAQ,gBAAgB;AACzF,SAASC,WAAW,QAAQ,6BAA6B;AACzD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,cAAc,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM;IAAEC;EAAS,CAAC,GAAGP,WAAW,CAAC,CAAC;EAClC,MAAM,CAACQ,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdsB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAM,CAACC,eAAe,EAAEC,gBAAgB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC5DjB,UAAU,CAACkB,MAAM,CAACC,kBAAkB,CAAC,CAAC,EACtCnB,UAAU,CAACkB,MAAM,CAACE,WAAW,CAAC,CAAC,CAChC,CAAC;MAEFZ,kBAAkB,CAACM,eAAe,CAACO,IAAI,CAAC;MACxCX,WAAW,CAACK,gBAAgB,CAACM,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM;IAAEa,QAAQ;IAAEC;EAAQ,CAAC,GAAGpB,QAAQ;EAEtC,MAAMqB,UAAU,GAAG,CACjB;IACEC,IAAI,EAAE/B,OAAO;IACbgC,KAAK,EAAE,qBAAqB;IAC5BC,KAAK,EAAEL,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEM,mBAAmB,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGC,QAAQ,CAACT,QAAQ,CAACM,mBAAmB,CAAC,GAAG,GAAG,KAAK;IACtHI,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,EAAEjC,OAAO;IACbkC,KAAK,EAAE,mBAAmB;IAC1BC,KAAK,EAAE,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEW,aAAa,KAAI,MAAM;IACxCD,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,EAAEhC,WAAW;IACjBiC,KAAK,EAAE,mBAAmB;IAC1BC,KAAK,EAAE,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEY,eAAe,KAAI,KAAK;IACzCF,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,EAAE9B,WAAW;IACjB+B,KAAK,EAAE,mBAAmB;IAC1BC,KAAK,EAAE,MAAM;IACbK,WAAW,EAAE;EACf,CAAC,CACF;EAED,IAAIxB,OAAO,EAAE;IACX,oBACER,OAAA;MAAKmC,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DpC,OAAA,CAACF,cAAc;QAACuC,IAAI,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAEV;EAEA,oBACEzC,OAAA;IAAKmC,SAAS,EAAC,OAAO;IAAAC,QAAA,gBAEpBpC,OAAA;MAASmC,SAAS,EAAC,sFAAsF;MAAAC,QAAA,eACvGpC,OAAA;QAAKmC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCpC,OAAA;UAAKmC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CpC,OAAA,CAACV,MAAM,CAACoD,EAAE;YACRC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9Bb,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAE9C,CAAAd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2B,UAAU,KAAI;UAAwC;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eAEZzC,OAAA,CAACV,MAAM,CAAC4D,CAAC;YACPP,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1ChB,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAExE,CAAAd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE8B,aAAa,KAAI;UAAwH;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5I,CAAC,eAEXzC,OAAA,CAACV,MAAM,CAAC+D,GAAG;YACTV,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1ChB,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAE1DpC,OAAA,CAACX,IAAI;cAACiE,EAAE,EAAC,QAAQ;cAACnB,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,gBAE1C,eAAApC,OAAA,CAACT,YAAY;gBAAC4C,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACPzC,OAAA,CAACX,IAAI;cAACiE,EAAE,EAAC,WAAW;cAACnB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAEjD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVzC,OAAA;MAASmC,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eAC1CpC,OAAA;QAAKmC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCpC,OAAA;UAAKmC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CpC,OAAA,CAACV,MAAM,CAACiE,EAAE;YACRZ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BW,WAAW,EAAE;cAAEZ,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BS,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBvB,SAAS,EAAC,wDAAwD;YAAAC,QAAA,EACnE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAEZzC,OAAA,CAACV,MAAM,CAAC4D,CAAC;YACPP,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BW,WAAW,EAAE;cAAEZ,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1CM,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBvB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAClD;UAID;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVzC,OAAA;MAASmC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eACpDpC,OAAA;QAAKmC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCpC,OAAA,CAACV,MAAM,CAACiE,EAAE;UACRZ,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BW,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BS,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBvB,SAAS,EAAC,qEAAqE;UAAAC,QAAA,EAChF;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEZzC,OAAA;UAAKmC,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEZ,UAAU,CAACmC,GAAG,CAAC,CAACC,SAAS,EAAEC,KAAK,kBAC/B7D,OAAA,CAACV,MAAM,CAAC+D,GAAG;YAETV,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BW,WAAW,EAAE;cAAEZ,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAEU,KAAK,GAAG;YAAI,CAAE;YAClDJ,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBvB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAE5BpC,OAAA;cAAKmC,SAAS,EAAC,yFAAyF;cAAAC,QAAA,eACtGpC,OAAA,CAAC4D,SAAS,CAACnC,IAAI;gBAACU,SAAS,EAAC;cAA6B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNzC,OAAA;cAAImC,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EACvDwB,SAAS,CAACjC;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACLzC,OAAA;cAAImC,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACrDwB,SAAS,CAAClC;YAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACLzC,OAAA;cAAGmC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACjCwB,SAAS,CAAC5B;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA,GAlBCoB,KAAK;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGTnC,QAAQ,CAACwD,MAAM,GAAG,CAAC,iBAClB9D,OAAA;MAASmC,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eAC1CpC,OAAA;QAAKmC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCpC,OAAA;UAAKmC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCpC,OAAA,CAACV,MAAM,CAACiE,EAAE;YACRZ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BW,WAAW,EAAE;cAAEZ,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BS,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBvB,SAAS,EAAC,wDAAwD;YAAAC,QAAA,EACnE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZzC,OAAA,CAACV,MAAM,CAAC4D,CAAC;YACPP,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BW,WAAW,EAAE;cAAEZ,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1CM,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBvB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAClC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENzC,OAAA;UAAKmC,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EACzD9B,QAAQ,CAACqD,GAAG,CAAC,CAACI,OAAO,EAAEF,KAAK,kBAC3B7D,OAAA,CAACV,MAAM,CAAC+D,GAAG;YAETV,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BW,WAAW,EAAE;cAAEZ,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAEU,KAAK,GAAG;YAAI,CAAE;YAClDJ,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBvB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,GAE3B2B,OAAO,CAACC,SAAS,iBAChBhE,OAAA;cACEiE,GAAG,EAAEF,OAAO,CAACC,SAAU;cACvBE,GAAG,EAAEH,OAAO,CAACI,IAAK;cAClBhC,SAAS,EAAC;YAAuC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CACF,eACDzC,OAAA;cAAImC,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAC1D2B,OAAO,CAACI;YAAI;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACLzC,OAAA;cAAGmC,SAAS,EAAC,eAAe;cAAAC,QAAA,EACzB2B,OAAO,CAAC/B;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA,GAnBCsB,OAAO,CAACK,EAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENzC,OAAA,CAACV,MAAM,CAAC+D,GAAG;UACTV,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BW,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BS,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBvB,SAAS,EAAC,aAAa;UAAAC,QAAA,eAEvBpC,OAAA,CAACX,IAAI;YAACiE,EAAE,EAAC,WAAW;YAACnB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,GAAC,mBAE/C,eAAApC,OAAA,CAACT,YAAY;cAAC4C,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACV,EAGArC,eAAe,CAAC0D,MAAM,GAAG,CAAC,iBACzB9D,OAAA;MAASmC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eACpDpC,OAAA;QAAKmC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCpC,OAAA,CAACV,MAAM,CAACiE,EAAE;UACRZ,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BW,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BS,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBvB,SAAS,EAAC,qEAAqE;UAAAC,QAAA,EAChF;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEZzC,OAAA;UAAKmC,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEhC,eAAe,CAACuD,GAAG,CAAC,CAACU,MAAM,EAAER,KAAK,kBACjC7D,OAAA,CAACV,MAAM,CAAC+D,GAAG;YAETV,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAE0B,KAAK,EAAE;YAAI,CAAE;YACpCd,WAAW,EAAE;cAAEZ,OAAO,EAAE,CAAC;cAAE0B,KAAK,EAAE;YAAE,CAAE;YACtCvB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAEU,KAAK,GAAG;YAAI,CAAE;YAClDJ,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBvB,SAAS,EAAC,yGAAyG;YAAAC,QAAA,EAElHiC,MAAM,CAACE,QAAQ,gBACdvE,OAAA;cACEiE,GAAG,EAAEI,MAAM,CAACE,QAAS;cACrBL,GAAG,EAAEG,MAAM,CAACF,IAAK;cACjBhC,SAAS,EAAC;YAAmG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G,CAAC,gBAEFzC,OAAA;cAAMmC,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAC5DiC,MAAM,CAACF;YAAI;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACP,GAjBI4B,MAAM,CAACD,EAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBJ,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENzC,OAAA,CAACV,MAAM,CAAC+D,GAAG;UACTV,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BW,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BS,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBvB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAE7BpC,OAAA,CAACX,IAAI;YAACiE,EAAE,EAAC,UAAU;YAACnB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,GAAC,kBAE9C,eAAApC,OAAA,CAACT,YAAY;cAAC4C,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACV,eAGDzC,OAAA;MAASmC,SAAS,EAAC,4CAA4C;MAAAC,QAAA,eAC7DpC,OAAA;QAAKmC,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDpC,OAAA,CAACV,MAAM,CAACiE,EAAE;UACRZ,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BW,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BS,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBvB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAChD;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEZzC,OAAA,CAACV,MAAM,CAAC4D,CAAC;UACPP,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BW,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBvB,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EAChE;QAGD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAEXzC,OAAA,CAACV,MAAM,CAAC+D,GAAG;UACTV,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BW,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CM,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBvB,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAE1DpC,OAAA,CAACX,IAAI;YAACiE,EAAE,EAAC,UAAU;YAACnB,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,cAE5C,eAAApC,OAAA,CAACT,YAAY;cAAC4C,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACPzC,OAAA,CAACX,IAAI;YAACiE,EAAE,EAAC,WAAW;YAACnB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAEjD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACvC,EAAA,CArVID,IAAI;EAAA,QACaL,WAAW;AAAA;AAAA4E,EAAA,GAD5BvE,IAAI;AAuVV,eAAeA,IAAI;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}