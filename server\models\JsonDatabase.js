const fs = require('fs');
const path = require('path');

class JsonDatabase {
  constructor() {
    this.dataPath = path.resolve(__dirname, '../../database/data.json');
    this.ensureDirectoryExists();
    this.data = this.loadData();
  }

  ensureDirectoryExists() {
    const dir = path.dirname(this.dataPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  }

  loadData() {
    try {
      if (fs.existsSync(this.dataPath)) {
        const data = fs.readFileSync(this.dataPath, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    }
    
    // Return default data structure
    return {
      admin_users: [],
      about_content: [],
      services: [],
      suppliers: [],
      product_categories: [],
      products: [],
      corporate_clients: [],
      news: [],
      contact_info: [],
      team_members: [],
      site_settings: []
    };
  }

  saveData() {
    try {
      fs.writeFileSync(this.dataPath, JSON.stringify(this.data, null, 2));
    } catch (error) {
      console.error('Error saving data:', error);
      throw error;
    }
  }

  async connect() {
    console.log('Connected to JSON database');
    return Promise.resolve();
  }

  async close() {
    console.log('JSON database connection closed');
    return Promise.resolve();
  }

  generateId(table) {
    const items = this.data[table] || [];
    return items.length > 0 ? Math.max(...items.map(item => item.id || 0)) + 1 : 1;
  }

  async run(sql, params = []) {
    // This is a simplified implementation for basic CRUD operations
    // In a real implementation, you'd parse the SQL
    return Promise.resolve({ id: this.generateId('temp'), changes: 1 });
  }

  async get(sql, params = []) {
    // Simplified implementation
    return Promise.resolve(null);
  }

  async all(sql, params = []) {
    // Simplified implementation
    return Promise.resolve([]);
  }

  // Helper methods for direct data access
  findAll(table, orderBy = 'id') {
    const items = this.data[table] || [];
    return [...items].sort((a, b) => {
      if (orderBy === 'order_index ASC, created_at DESC') {
        return (a.order_index || 0) - (b.order_index || 0) || 
               new Date(b.created_at || 0) - new Date(a.created_at || 0);
      }
      return a.id - b.id;
    });
  }

  findById(table, id) {
    const items = this.data[table] || [];
    return items.find(item => item.id === parseInt(id));
  }

  findByField(table, field, value) {
    const items = this.data[table] || [];
    return items.find(item => item[field] === value);
  }

  create(table, data) {
    if (!this.data[table]) {
      this.data[table] = [];
    }
    
    const newItem = {
      id: this.generateId(table),
      ...data,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    this.data[table].push(newItem);
    this.saveData();
    return newItem;
  }

  update(table, id, data) {
    const items = this.data[table] || [];
    const index = items.findIndex(item => item.id === parseInt(id));
    
    if (index !== -1) {
      items[index] = {
        ...items[index],
        ...data,
        updated_at: new Date().toISOString()
      };
      this.saveData();
      return items[index];
    }
    
    return null;
  }

  delete(table, id) {
    const items = this.data[table] || [];
    const index = items.findIndex(item => item.id === parseInt(id));
    
    if (index !== -1) {
      items.splice(index, 1);
      this.saveData();
      return true;
    }
    
    return false;
  }

  count(table) {
    return (this.data[table] || []).length;
  }

  async initializeTables() {
    console.log('Initializing JSON database tables');
    
    // Seed initial data if empty
    await this.seedInitialData();
    
    console.log('JSON database initialized successfully');
  }

  async seedInitialData() {
    // Check if admin user exists
    const adminExists = this.findByField('admin_users', 'username', 'admin');
    
    if (!adminExists) {
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD || 'admin123', 10);
      
      this.create('admin_users', {
        username: 'admin',
        password_hash: hashedPassword,
        email: '<EMAIL>'
      });
      console.log('Default admin user created');
    }

    // Seed basic contact info
    const contactFields = [
      ['company_name', 'Al-Fayasel Drugstore'],
      ['phone', '******-567-8900'],
      ['email', '<EMAIL>'],
      ['address', '123 Pharmaceutical Street, Medical District, City, Country'],
      ['working_hours', 'Monday - Friday: 8:00 AM - 6:00 PM'],
      ['google_map_url', 'https://maps.google.com/embed?pb=!1m18!1m12!1m3!1d3024.*********!2d-74.0059413!3d40.7127753!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNDDCsDQyJzQ2LjAiTiA3NMKwMDAnMjEuNCJX!5e0!3m2!1sen!2sus!4v*********0123!5m2!1sen!2sus']
    ];

    for (const [key, value] of contactFields) {
      const exists = this.findByField('contact_info', 'field_name', key);
      if (!exists) {
        this.create('contact_info', {
          field_name: key,
          field_value: value
        });
      }
    }

    // Seed basic site settings
    const siteSettings = [
      ['site_title', 'Al-Fayasel Drugstore - Pharmaceutical Distribution Excellence'],
      ['site_description', 'Leading wholesale pharmaceutical distributor serving corporate clients with quality medicines and reliable service.'],
      ['hero_title', 'Pharmaceutical Distribution Excellence'],
      ['hero_subtitle', 'Your trusted partner in wholesale pharmaceutical distribution, serving corporate clients with quality and reliability.'],
      ['company_established', '2010'],
      ['total_clients', '150+'],
      ['total_suppliers', '50+']
    ];

    for (const [key, value] of siteSettings) {
      const exists = this.findByField('site_settings', 'setting_key', key);
      if (!exists) {
        this.create('site_settings', {
          setting_key: key,
          setting_value: value
        });
      }
    }

    console.log('Initial data seeded successfully');
  }
}

module.exports = JsonDatabase;
