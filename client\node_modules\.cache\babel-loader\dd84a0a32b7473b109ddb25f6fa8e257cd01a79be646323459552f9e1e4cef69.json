{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\admin\\\\components\\\\Modal.js\";\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaTimes } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Modal = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md',\n  showCloseButton = true\n}) => {\n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-2xl',\n    lg: 'max-w-4xl',\n    xl: 'max-w-6xl'\n  };\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-50 overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        exit: {\n          opacity: 0\n        },\n        className: \"fixed inset-0 bg-black bg-opacity-50\",\n        onClick: onClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex min-h-full items-center justify-center p-4\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.95,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            scale: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            scale: 0.95,\n            y: 20\n          },\n          transition: {\n            duration: 0.2\n          },\n          className: `relative w-full ${sizeClasses[size]} bg-white rounded-lg shadow-xl`,\n          onClick: e => e.stopPropagation(),\n          children: [(title || showCloseButton) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n            children: [title && /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-800\",\n              children: title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 21\n            }, this), showCloseButton && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onClose,\n              className: \"text-gray-400 hover:text-gray-600 transition-colors duration-200\",\n              children: /*#__PURE__*/_jsxDEV(FaTimes, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: children\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_c = Modal;\nexport default Modal;\nvar _c;\n$RefreshReg$(_c, \"Modal\");", "map": {"version": 3, "names": ["React", "motion", "AnimatePresence", "FaTimes", "jsxDEV", "_jsxDEV", "Modal", "isOpen", "onClose", "title", "children", "size", "showCloseButton", "sizeClasses", "sm", "md", "lg", "xl", "className", "div", "initial", "opacity", "animate", "exit", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "scale", "y", "transition", "duration", "e", "stopPropagation", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/admin/components/Modal.js"], "sourcesContent": ["import React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaTimes } from 'react-icons/fa';\n\nconst Modal = ({ \n  isOpen, \n  onClose, \n  title, \n  children, \n  size = 'md',\n  showCloseButton = true \n}) => {\n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-2xl',\n    lg: 'max-w-4xl',\n    xl: 'max-w-6xl'\n  };\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n          {/* Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black bg-opacity-50\"\n            onClick={onClose}\n          />\n          \n          {/* Modal */}\n          <div className=\"flex min-h-full items-center justify-center p-4\">\n            <motion.div\n              initial={{ opacity: 0, scale: 0.95, y: 20 }}\n              animate={{ opacity: 1, scale: 1, y: 0 }}\n              exit={{ opacity: 0, scale: 0.95, y: 20 }}\n              transition={{ duration: 0.2 }}\n              className={`relative w-full ${sizeClasses[size]} bg-white rounded-lg shadow-xl`}\n              onClick={(e) => e.stopPropagation()}\n            >\n              {/* Header */}\n              {(title || showCloseButton) && (\n                <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n                  {title && (\n                    <h3 className=\"text-lg font-semibold text-gray-800\">\n                      {title}\n                    </h3>\n                  )}\n                  {showCloseButton && (\n                    <button\n                      onClick={onClose}\n                      className=\"text-gray-400 hover:text-gray-600 transition-colors duration-200\"\n                    >\n                      <FaTimes size={20} />\n                    </button>\n                  )}\n                </div>\n              )}\n              \n              {/* Content */}\n              <div className=\"p-6\">\n                {children}\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default Modal;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,KAAK,GAAGA,CAAC;EACbC,MAAM;EACNC,OAAO;EACPC,KAAK;EACLC,QAAQ;EACRC,IAAI,GAAG,IAAI;EACXC,eAAe,GAAG;AACpB,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE;EACN,CAAC;EAED,oBACEZ,OAAA,CAACH,eAAe;IAAAQ,QAAA,EACbH,MAAM,iBACLF,OAAA;MAAKa,SAAS,EAAC,oCAAoC;MAAAR,QAAA,gBAEjDL,OAAA,CAACJ,MAAM,CAACkB,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBE,IAAI,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACrBH,SAAS,EAAC,sCAAsC;QAChDM,OAAO,EAAEhB;MAAQ;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAGFvB,OAAA;QAAKa,SAAS,EAAC,iDAAiD;QAAAR,QAAA,eAC9DL,OAAA,CAACJ,MAAM,CAACkB,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEQ,KAAK,EAAE,IAAI;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC5CR,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEQ,KAAK,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UACxCP,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEQ,KAAK,EAAE,IAAI;YAAEC,CAAC,EAAE;UAAG,CAAE;UACzCC,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9Bd,SAAS,EAAE,mBAAmBL,WAAW,CAACF,IAAI,CAAC,gCAAiC;UAChFa,OAAO,EAAGS,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;UAAAxB,QAAA,GAGnC,CAACD,KAAK,IAAIG,eAAe,kBACxBP,OAAA;YAAKa,SAAS,EAAC,gEAAgE;YAAAR,QAAA,GAC5ED,KAAK,iBACJJ,OAAA;cAAIa,SAAS,EAAC,qCAAqC;cAAAR,QAAA,EAChDD;YAAK;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACL,EACAhB,eAAe,iBACdP,OAAA;cACEmB,OAAO,EAAEhB,OAAQ;cACjBU,SAAS,EAAC,kEAAkE;cAAAR,QAAA,eAE5EL,OAAA,CAACF,OAAO;gBAACQ,IAAI,EAAE;cAAG;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,eAGDvB,OAAA;YAAKa,SAAS,EAAC,KAAK;YAAAR,QAAA,EACjBA;UAAQ;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAEtB,CAAC;AAACO,EAAA,GAnEI7B,KAAK;AAqEX,eAAeA,KAAK;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}