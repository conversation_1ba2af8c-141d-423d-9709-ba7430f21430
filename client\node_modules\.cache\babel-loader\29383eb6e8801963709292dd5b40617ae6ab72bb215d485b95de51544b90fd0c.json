{"ast": null, "code": "'use strict';\n\nfunction _interopDefault(ex) {\n  return ex && typeof ex === 'object' && 'default' in ex ? ex['default'] : ex;\n}\nvar React = require('react');\nvar React__default = _interopDefault(React);\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\nvar canUseDOM = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nfunction withSideEffect(reducePropsToState, handleStateChangeOnClient, mapStateOnServer) {\n  if (typeof reducePropsToState !== 'function') {\n    throw new Error('Expected reducePropsToState to be a function.');\n  }\n  if (typeof handleStateChangeOnClient !== 'function') {\n    throw new Error('Expected handleStateChangeOnClient to be a function.');\n  }\n  if (typeof mapStateOnServer !== 'undefined' && typeof mapStateOnServer !== 'function') {\n    throw new Error('Expected mapStateOnServer to either be undefined or a function.');\n  }\n  function getDisplayName(WrappedComponent) {\n    return WrappedComponent.displayName || WrappedComponent.name || 'Component';\n  }\n  return function wrap(WrappedComponent) {\n    if (typeof WrappedComponent !== 'function') {\n      throw new Error('Expected WrappedComponent to be a React component.');\n    }\n    var mountedInstances = [];\n    var state;\n    function emitChange() {\n      state = reducePropsToState(mountedInstances.map(function (instance) {\n        return instance.props;\n      }));\n      if (SideEffect.canUseDOM) {\n        handleStateChangeOnClient(state);\n      } else if (mapStateOnServer) {\n        state = mapStateOnServer(state);\n      }\n    }\n    var SideEffect = /*#__PURE__*/\n    function (_PureComponent) {\n      _inheritsLoose(SideEffect, _PureComponent);\n      function SideEffect() {\n        return _PureComponent.apply(this, arguments) || this;\n      }\n\n      // Try to use displayName of wrapped component\n      // Expose canUseDOM so tests can monkeypatch it\n      SideEffect.peek = function peek() {\n        return state;\n      };\n      SideEffect.rewind = function rewind() {\n        if (SideEffect.canUseDOM) {\n          throw new Error('You may only call rewind() on the server. Call peek() to read the current state.');\n        }\n        var recordedState = state;\n        state = undefined;\n        mountedInstances = [];\n        return recordedState;\n      };\n      var _proto = SideEffect.prototype;\n      _proto.UNSAFE_componentWillMount = function UNSAFE_componentWillMount() {\n        mountedInstances.push(this);\n        emitChange();\n      };\n      _proto.componentDidUpdate = function componentDidUpdate() {\n        emitChange();\n      };\n      _proto.componentWillUnmount = function componentWillUnmount() {\n        var index = mountedInstances.indexOf(this);\n        mountedInstances.splice(index, 1);\n        emitChange();\n      };\n      _proto.render = function render() {\n        return React__default.createElement(WrappedComponent, this.props);\n      };\n      return SideEffect;\n    }(React.PureComponent);\n    _defineProperty(SideEffect, \"displayName\", \"SideEffect(\" + getDisplayName(WrappedComponent) + \")\");\n    _defineProperty(SideEffect, \"canUseDOM\", canUseDOM);\n    return SideEffect;\n  };\n}\nmodule.exports = withSideEffect;", "map": {"version": 3, "names": ["_interopDefault", "ex", "React", "require", "React__default", "_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "prototype", "create", "constructor", "__proto__", "canUseDOM", "window", "document", "createElement", "withSideEffect", "reducePropsToState", "handleStateChangeOnClient", "mapStateOnServer", "Error", "getDisplayName", "WrappedComponent", "displayName", "name", "wrap", "mountedInstances", "state", "emitChange", "map", "instance", "props", "SideEffect", "_PureComponent", "apply", "arguments", "peek", "rewind", "recordedState", "undefined", "_proto", "UNSAFE_componentWillMount", "push", "componentDidUpdate", "componentWillUnmount", "index", "indexOf", "splice", "render", "PureComponent", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/node_modules/react-side-effect/lib/index.js"], "sourcesContent": ["'use strict';\n\nfunction _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }\n\nvar React = require('react');\nvar React__default = _interopDefault(React);\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nfunction withSideEffect(reducePropsToState, handleStateChangeOnClient, mapStateOnServer) {\n  if (typeof reducePropsToState !== 'function') {\n    throw new Error('Expected reducePropsToState to be a function.');\n  }\n\n  if (typeof handleStateChangeOnClient !== 'function') {\n    throw new Error('Expected handleStateChangeOnClient to be a function.');\n  }\n\n  if (typeof mapStateOnServer !== 'undefined' && typeof mapStateOnServer !== 'function') {\n    throw new Error('Expected mapStateOnServer to either be undefined or a function.');\n  }\n\n  function getDisplayName(WrappedComponent) {\n    return WrappedComponent.displayName || WrappedComponent.name || 'Component';\n  }\n\n  return function wrap(WrappedComponent) {\n    if (typeof WrappedComponent !== 'function') {\n      throw new Error('Expected WrappedComponent to be a React component.');\n    }\n\n    var mountedInstances = [];\n    var state;\n\n    function emitChange() {\n      state = reducePropsToState(mountedInstances.map(function (instance) {\n        return instance.props;\n      }));\n\n      if (SideEffect.canUseDOM) {\n        handleStateChangeOnClient(state);\n      } else if (mapStateOnServer) {\n        state = mapStateOnServer(state);\n      }\n    }\n\n    var SideEffect =\n    /*#__PURE__*/\n    function (_PureComponent) {\n      _inheritsLoose(SideEffect, _PureComponent);\n\n      function SideEffect() {\n        return _PureComponent.apply(this, arguments) || this;\n      }\n\n      // Try to use displayName of wrapped component\n      // Expose canUseDOM so tests can monkeypatch it\n      SideEffect.peek = function peek() {\n        return state;\n      };\n\n      SideEffect.rewind = function rewind() {\n        if (SideEffect.canUseDOM) {\n          throw new Error('You may only call rewind() on the server. Call peek() to read the current state.');\n        }\n\n        var recordedState = state;\n        state = undefined;\n        mountedInstances = [];\n        return recordedState;\n      };\n\n      var _proto = SideEffect.prototype;\n\n      _proto.UNSAFE_componentWillMount = function UNSAFE_componentWillMount() {\n        mountedInstances.push(this);\n        emitChange();\n      };\n\n      _proto.componentDidUpdate = function componentDidUpdate() {\n        emitChange();\n      };\n\n      _proto.componentWillUnmount = function componentWillUnmount() {\n        var index = mountedInstances.indexOf(this);\n        mountedInstances.splice(index, 1);\n        emitChange();\n      };\n\n      _proto.render = function render() {\n        return React__default.createElement(WrappedComponent, this.props);\n      };\n\n      return SideEffect;\n    }(React.PureComponent);\n\n    _defineProperty(SideEffect, \"displayName\", \"SideEffect(\" + getDisplayName(WrappedComponent) + \")\");\n\n    _defineProperty(SideEffect, \"canUseDOM\", canUseDOM);\n\n    return SideEffect;\n  };\n}\n\nmodule.exports = withSideEffect;\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAeA,CAAEC,EAAE,EAAE;EAAE,OAAQA,EAAE,IAAK,OAAOA,EAAE,KAAK,QAAS,IAAI,SAAS,IAAIA,EAAE,GAAIA,EAAE,CAAC,SAAS,CAAC,GAAGA,EAAE;AAAE;AAEjH,IAAIC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC5B,IAAIC,cAAc,GAAGJ,eAAe,CAACE,KAAK,CAAC;AAE3C,SAASG,eAAeA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;EACxC,IAAID,GAAG,IAAID,GAAG,EAAE;IACdG,MAAM,CAACC,cAAc,CAACJ,GAAG,EAAEC,GAAG,EAAE;MAC9BC,KAAK,EAAEA,KAAK;MACZG,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLP,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAClB;EAEA,OAAOF,GAAG;AACZ;AAEA,SAASQ,cAAcA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAC5CD,QAAQ,CAACE,SAAS,GAAGR,MAAM,CAACS,MAAM,CAACF,UAAU,CAACC,SAAS,CAAC;EACxDF,QAAQ,CAACE,SAAS,CAACE,WAAW,GAAGJ,QAAQ;EACzCA,QAAQ,CAACK,SAAS,GAAGJ,UAAU;AACjC;AAEA,IAAIK,SAAS,GAAG,CAAC,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACC,aAAa,CAAC;AACrG,SAASC,cAAcA,CAACC,kBAAkB,EAAEC,yBAAyB,EAAEC,gBAAgB,EAAE;EACvF,IAAI,OAAOF,kBAAkB,KAAK,UAAU,EAAE;IAC5C,MAAM,IAAIG,KAAK,CAAC,+CAA+C,CAAC;EAClE;EAEA,IAAI,OAAOF,yBAAyB,KAAK,UAAU,EAAE;IACnD,MAAM,IAAIE,KAAK,CAAC,sDAAsD,CAAC;EACzE;EAEA,IAAI,OAAOD,gBAAgB,KAAK,WAAW,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;IACrF,MAAM,IAAIC,KAAK,CAAC,iEAAiE,CAAC;EACpF;EAEA,SAASC,cAAcA,CAACC,gBAAgB,EAAE;IACxC,OAAOA,gBAAgB,CAACC,WAAW,IAAID,gBAAgB,CAACE,IAAI,IAAI,WAAW;EAC7E;EAEA,OAAO,SAASC,IAAIA,CAACH,gBAAgB,EAAE;IACrC,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;MAC1C,MAAM,IAAIF,KAAK,CAAC,oDAAoD,CAAC;IACvE;IAEA,IAAIM,gBAAgB,GAAG,EAAE;IACzB,IAAIC,KAAK;IAET,SAASC,UAAUA,CAAA,EAAG;MACpBD,KAAK,GAAGV,kBAAkB,CAACS,gBAAgB,CAACG,GAAG,CAAC,UAAUC,QAAQ,EAAE;QAClE,OAAOA,QAAQ,CAACC,KAAK;MACvB,CAAC,CAAC,CAAC;MAEH,IAAIC,UAAU,CAACpB,SAAS,EAAE;QACxBM,yBAAyB,CAACS,KAAK,CAAC;MAClC,CAAC,MAAM,IAAIR,gBAAgB,EAAE;QAC3BQ,KAAK,GAAGR,gBAAgB,CAACQ,KAAK,CAAC;MACjC;IACF;IAEA,IAAIK,UAAU,GACd;IACA,UAAUC,cAAc,EAAE;MACxB5B,cAAc,CAAC2B,UAAU,EAAEC,cAAc,CAAC;MAE1C,SAASD,UAAUA,CAAA,EAAG;QACpB,OAAOC,cAAc,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;MACtD;;MAEA;MACA;MACAH,UAAU,CAACI,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG;QAChC,OAAOT,KAAK;MACd,CAAC;MAEDK,UAAU,CAACK,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;QACpC,IAAIL,UAAU,CAACpB,SAAS,EAAE;UACxB,MAAM,IAAIQ,KAAK,CAAC,kFAAkF,CAAC;QACrG;QAEA,IAAIkB,aAAa,GAAGX,KAAK;QACzBA,KAAK,GAAGY,SAAS;QACjBb,gBAAgB,GAAG,EAAE;QACrB,OAAOY,aAAa;MACtB,CAAC;MAED,IAAIE,MAAM,GAAGR,UAAU,CAACxB,SAAS;MAEjCgC,MAAM,CAACC,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;QACtEf,gBAAgB,CAACgB,IAAI,CAAC,IAAI,CAAC;QAC3Bd,UAAU,CAAC,CAAC;MACd,CAAC;MAEDY,MAAM,CAACG,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;QACxDf,UAAU,CAAC,CAAC;MACd,CAAC;MAEDY,MAAM,CAACI,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;QAC5D,IAAIC,KAAK,GAAGnB,gBAAgB,CAACoB,OAAO,CAAC,IAAI,CAAC;QAC1CpB,gBAAgB,CAACqB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACjCjB,UAAU,CAAC,CAAC;MACd,CAAC;MAEDY,MAAM,CAACQ,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;QAChC,OAAOrD,cAAc,CAACoB,aAAa,CAACO,gBAAgB,EAAE,IAAI,CAACS,KAAK,CAAC;MACnE,CAAC;MAED,OAAOC,UAAU;IACnB,CAAC,CAACvC,KAAK,CAACwD,aAAa,CAAC;IAEtBrD,eAAe,CAACoC,UAAU,EAAE,aAAa,EAAE,aAAa,GAAGX,cAAc,CAACC,gBAAgB,CAAC,GAAG,GAAG,CAAC;IAElG1B,eAAe,CAACoC,UAAU,EAAE,WAAW,EAAEpB,SAAS,CAAC;IAEnD,OAAOoB,UAAU;EACnB,CAAC;AACH;AAEAkB,MAAM,CAACC,OAAO,GAAGnC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}