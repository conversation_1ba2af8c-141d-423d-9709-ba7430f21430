{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\pages\\\\NewsDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Helmet } from 'react-helmet';\nimport { FaArrowLeft, FaCalendar, FaUser } from 'react-icons/fa';\nimport apiService from '../services/api';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NewsDetail = () => {\n  _s();\n  var _article$content;\n  const {\n    id\n  } = useParams();\n  const [article, setArticle] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchArticle();\n  }, [id]);\n  const fetchArticle = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.public.getNewsArticle(id);\n      setArticle(response.data);\n      setError(null);\n    } catch (error) {\n      console.error('Error fetching article:', error);\n      setError('Failed to load article');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pt-20 min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        size: \"xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !article) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pt-20 min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(ErrorMessage, {\n        message: error || 'Article not found'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pt-20\",\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: [article.title, \" - Al-Fayasel Drugstore\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: article.excerpt || ((_article$content = article.content) === null || _article$content === void 0 ? void 0 : _article$content.substring(0, 160))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/news\",\n            className: \"inline-flex items-center text-primary-green hover:text-primary-green-dark mb-8 transition-colors duration-200\",\n            children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {\n              className: \"mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), \"Back to News\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.article, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8\n            },\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n            children: [article.image_url && /*#__PURE__*/_jsxDEV(\"img\", {\n              src: article.image_url,\n              alt: article.title,\n              className: \"w-full h-64 lg:h-96 object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4 text-sm text-gray-500 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCalendar, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: formatDate(article.published_at || article.created_at)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this), article.author && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(FaUser, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 95,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: article.author\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 96,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl lg:text-4xl font-bold text-primary-green mb-6\",\n                children: article.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"prose prose-lg max-w-none text-gray-600 leading-relaxed\",\n                dangerouslySetInnerHTML: {\n                  __html: article.content\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(NewsDetail, \"tEkxbRkc6KfY55D3gdTiro2sAyA=\", false, function () {\n  return [useParams];\n});\n_c = NewsDetail;\nexport default NewsDetail;\nvar _c;\n$RefreshReg$(_c, \"NewsDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "motion", "<PERSON><PERSON><PERSON>", "FaArrowLeft", "FaCalendar", "FaUser", "apiService", "LoadingSpinner", "ErrorMessage", "jsxDEV", "_jsxDEV", "NewsDetail", "_s", "_article$content", "id", "article", "setArticle", "loading", "setLoading", "error", "setError", "fetchArticle", "response", "public", "getNewsArticle", "data", "console", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "title", "name", "content", "excerpt", "substring", "to", "initial", "opacity", "y", "animate", "transition", "duration", "image_url", "src", "alt", "published_at", "created_at", "author", "dangerouslySetInnerHTML", "__html", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/pages/NewsDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Helmet } from 'react-helmet';\nimport { FaArrowLeft, FaCalendar, FaUser } from 'react-icons/fa';\nimport apiService from '../services/api';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\n\nconst NewsDetail = () => {\n  const { id } = useParams();\n  const [article, setArticle] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchArticle();\n  }, [id]);\n\n  const fetchArticle = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.public.getNewsArticle(id);\n      setArticle(response.data);\n      setError(null);\n    } catch (error) {\n      console.error('Error fetching article:', error);\n      setError('Failed to load article');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"pt-20 min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"xl\" />\n      </div>\n    );\n  }\n\n  if (error || !article) {\n    return (\n      <div className=\"pt-20 min-h-screen flex items-center justify-center\">\n        <ErrorMessage message={error || 'Article not found'} />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"pt-20\">\n      <Helmet>\n        <title>{article.title} - Al-Fayasel Drugstore</title>\n        <meta name=\"description\" content={article.excerpt || article.content?.substring(0, 160)} />\n      </Helmet>\n\n      <section className=\"py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <Link to=\"/news\" className=\"inline-flex items-center text-primary-green hover:text-primary-green-dark mb-8 transition-colors duration-200\">\n              <FaArrowLeft className=\"mr-2\" />\n              Back to News\n            </Link>\n            \n            <motion.article\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"bg-white rounded-lg shadow-lg overflow-hidden\"\n            >\n              {article.image_url && (\n                <img\n                  src={article.image_url}\n                  alt={article.title}\n                  className=\"w-full h-64 lg:h-96 object-cover\"\n                />\n              )}\n              \n              <div className=\"p-8\">\n                <div className=\"flex items-center space-x-4 text-sm text-gray-500 mb-4\">\n                  <div className=\"flex items-center space-x-1\">\n                    <FaCalendar />\n                    <span>{formatDate(article.published_at || article.created_at)}</span>\n                  </div>\n                  {article.author && (\n                    <div className=\"flex items-center space-x-1\">\n                      <FaUser />\n                      <span>{article.author}</span>\n                    </div>\n                  )}\n                </div>\n                \n                <h1 className=\"text-3xl lg:text-4xl font-bold text-primary-green mb-6\">\n                  {article.title}\n                </h1>\n                \n                <div \n                  className=\"prose prose-lg max-w-none text-gray-600 leading-relaxed\"\n                  dangerouslySetInnerHTML={{ __html: article.content }}\n                />\n              </div>\n            </motion.article>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default NewsDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,WAAW,EAAEC,UAAU,EAAEC,MAAM,QAAQ,gBAAgB;AAChE,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,gBAAA;EACvB,MAAM;IAAEC;EAAG,CAAC,GAAGf,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACduB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACP,EAAE,CAAC,CAAC;EAER,MAAMO,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMhB,UAAU,CAACiB,MAAM,CAACC,cAAc,CAACV,EAAE,CAAC;MAC3DE,UAAU,CAACM,QAAQ,CAACG,IAAI,CAAC;MACzBL,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,wBAAwB,CAAC;IACpC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,IAAIhB,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKwB,SAAS,EAAC,qDAAqD;MAAAC,QAAA,eAClEzB,OAAA,CAACH,cAAc;QAAC6B,IAAI,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAEV;EAEA,IAAIrB,KAAK,IAAI,CAACJ,OAAO,EAAE;IACrB,oBACEL,OAAA;MAAKwB,SAAS,EAAC,qDAAqD;MAAAC,QAAA,eAClEzB,OAAA,CAACF,YAAY;QAACiC,OAAO,EAAEtB,KAAK,IAAI;MAAoB;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC;EAEV;EAEA,oBACE9B,OAAA;IAAKwB,SAAS,EAAC,OAAO;IAAAC,QAAA,gBACpBzB,OAAA,CAACR,MAAM;MAAAiC,QAAA,gBACLzB,OAAA;QAAAyB,QAAA,GAAQpB,OAAO,CAAC2B,KAAK,EAAC,yBAAuB;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrD9B,OAAA;QAAMiC,IAAI,EAAC,aAAa;QAACC,OAAO,EAAE7B,OAAO,CAAC8B,OAAO,MAAAhC,gBAAA,GAAIE,OAAO,CAAC6B,OAAO,cAAA/B,gBAAA,uBAAfA,gBAAA,CAAiBiC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;MAAC;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrF,CAAC,eAET9B,OAAA;MAASwB,SAAS,EAAC,OAAO;MAAAC,QAAA,eACxBzB,OAAA;QAAKwB,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCzB,OAAA;UAAKwB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzB,OAAA,CAACV,IAAI;YAAC+C,EAAE,EAAC,OAAO;YAACb,SAAS,EAAC,+GAA+G;YAAAC,QAAA,gBACxIzB,OAAA,CAACP,WAAW;cAAC+B,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEP9B,OAAA,CAACT,MAAM,CAACc,OAAO;YACbiC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BnB,SAAS,EAAC,+CAA+C;YAAAC,QAAA,GAExDpB,OAAO,CAACuC,SAAS,iBAChB5C,OAAA;cACE6C,GAAG,EAAExC,OAAO,CAACuC,SAAU;cACvBE,GAAG,EAAEzC,OAAO,CAAC2B,KAAM;cACnBR,SAAS,EAAC;YAAkC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CACF,eAED9B,OAAA;cAAKwB,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBzB,OAAA;gBAAKwB,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,gBACrEzB,OAAA;kBAAKwB,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzB,OAAA,CAACN,UAAU;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACd9B,OAAA;oBAAAyB,QAAA,EAAOR,UAAU,CAACZ,OAAO,CAAC0C,YAAY,IAAI1C,OAAO,CAAC2C,UAAU;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,EACLzB,OAAO,CAAC4C,MAAM,iBACbjD,OAAA;kBAAKwB,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzB,OAAA,CAACL,MAAM;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACV9B,OAAA;oBAAAyB,QAAA,EAAOpB,OAAO,CAAC4C;kBAAM;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN9B,OAAA;gBAAIwB,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,EACnEpB,OAAO,CAAC2B;cAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAEL9B,OAAA;gBACEwB,SAAS,EAAC,yDAAyD;gBACnE0B,uBAAuB,EAAE;kBAAEC,MAAM,EAAE9C,OAAO,CAAC6B;gBAAQ;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC5B,EAAA,CA1GID,UAAU;EAAA,QACCZ,SAAS;AAAA;AAAA+D,EAAA,GADpBnD,UAAU;AA4GhB,eAAeA,UAAU;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}