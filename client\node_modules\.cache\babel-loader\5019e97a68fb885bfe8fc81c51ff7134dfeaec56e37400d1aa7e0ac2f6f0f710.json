{"ast": null, "code": "import { isString } from '../utils.mjs';\nconst createUnitType = unit => ({\n  test: v => isString(v) && v.endsWith(unit) && v.split(\" \").length === 1,\n  parse: parseFloat,\n  transform: v => `${v}${unit}`\n});\nconst degrees = createUnitType(\"deg\");\nconst percent = createUnitType(\"%\");\nconst px = createUnitType(\"px\");\nconst vh = createUnitType(\"vh\");\nconst vw = createUnitType(\"vw\");\nconst progressPercentage = {\n  ...percent,\n  parse: v => percent.parse(v) / 100,\n  transform: v => percent.transform(v * 100)\n};\nexport { degrees, percent, progressPercentage, px, vh, vw };", "map": {"version": 3, "names": ["isString", "createUnitType", "unit", "test", "v", "endsWith", "split", "length", "parse", "parseFloat", "transform", "degrees", "percent", "px", "vh", "vw", "progressPercentage"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/node_modules/framer-motion/dist/es/value/types/numbers/units.mjs"], "sourcesContent": ["import { isString } from '../utils.mjs';\n\nconst createUnitType = (unit) => ({\n    test: (v) => isString(v) && v.endsWith(unit) && v.split(\" \").length === 1,\n    parse: parseFloat,\n    transform: (v) => `${v}${unit}`,\n});\nconst degrees = createUnitType(\"deg\");\nconst percent = createUnitType(\"%\");\nconst px = createUnitType(\"px\");\nconst vh = createUnitType(\"vh\");\nconst vw = createUnitType(\"vw\");\nconst progressPercentage = {\n    ...percent,\n    parse: (v) => percent.parse(v) / 100,\n    transform: (v) => percent.transform(v * 100),\n};\n\nexport { degrees, percent, progressPercentage, px, vh, vw };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AAEvC,MAAMC,cAAc,GAAIC,IAAI,KAAM;EAC9BC,IAAI,EAAGC,CAAC,IAAKJ,QAAQ,CAACI,CAAC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAACH,IAAI,CAAC,IAAIE,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,KAAK,CAAC;EACzEC,KAAK,EAAEC,UAAU;EACjBC,SAAS,EAAGN,CAAC,IAAK,GAAGA,CAAC,GAAGF,IAAI;AACjC,CAAC,CAAC;AACF,MAAMS,OAAO,GAAGV,cAAc,CAAC,KAAK,CAAC;AACrC,MAAMW,OAAO,GAAGX,cAAc,CAAC,GAAG,CAAC;AACnC,MAAMY,EAAE,GAAGZ,cAAc,CAAC,IAAI,CAAC;AAC/B,MAAMa,EAAE,GAAGb,cAAc,CAAC,IAAI,CAAC;AAC/B,MAAMc,EAAE,GAAGd,cAAc,CAAC,IAAI,CAAC;AAC/B,MAAMe,kBAAkB,GAAG;EACvB,GAAGJ,OAAO;EACVJ,KAAK,EAAGJ,CAAC,IAAKQ,OAAO,CAACJ,KAAK,CAACJ,CAAC,CAAC,GAAG,GAAG;EACpCM,SAAS,EAAGN,CAAC,IAAKQ,OAAO,CAACF,SAAS,CAACN,CAAC,GAAG,GAAG;AAC/C,CAAC;AAED,SAASO,OAAO,EAAEC,OAAO,EAAEI,kBAAkB,EAAEH,EAAE,EAAEC,EAAE,EAAEC,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}