{"ast": null, "code": "import { createContext } from 'react';\n\n/**\n * @public\n */\nconst PresenceContext = createContext(null);\nexport { PresenceContext };", "map": {"version": 3, "names": ["createContext", "PresenceContext"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/node_modules/framer-motion/dist/es/context/PresenceContext.mjs"], "sourcesContent": ["import { createContext } from 'react';\n\n/**\n * @public\n */\nconst PresenceContext = createContext(null);\n\nexport { PresenceContext };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;;AAErC;AACA;AACA;AACA,MAAMC,eAAe,GAAGD,aAAa,CAAC,IAAI,CAAC;AAE3C,SAASC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}