{"ast": null, "code": "function renderHTML(element, {\n  style,\n  vars\n}, styleProp, projection) {\n  Object.assign(element.style, style, projection && projection.getProjectionStyles(styleProp));\n  // Loop over any CSS variables and assign those.\n  for (const key in vars) {\n    element.style.setProperty(key, vars[key]);\n  }\n}\nexport { renderHTML };", "map": {"version": 3, "names": ["renderHTML", "element", "style", "vars", "styleProp", "projection", "Object", "assign", "getProjectionStyles", "key", "setProperty"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/node_modules/framer-motion/dist/es/render/html/utils/render.mjs"], "sourcesContent": ["function renderHTML(element, { style, vars }, styleProp, projection) {\n    Object.assign(element.style, style, projection && projection.getProjectionStyles(styleProp));\n    // Loop over any CSS variables and assign those.\n    for (const key in vars) {\n        element.style.setProperty(key, vars[key]);\n    }\n}\n\nexport { renderHTML };\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,OAAO,EAAE;EAAEC,KAAK;EAAEC;AAAK,CAAC,EAAEC,SAAS,EAAEC,UAAU,EAAE;EACjEC,MAAM,CAACC,MAAM,CAACN,OAAO,CAACC,KAAK,EAAEA,KAAK,EAAEG,UAAU,IAAIA,UAAU,CAACG,mBAAmB,CAACJ,SAAS,CAAC,CAAC;EAC5F;EACA,KAAK,MAAMK,GAAG,IAAIN,IAAI,EAAE;IACpBF,OAAO,CAACC,KAAK,CAACQ,WAAW,CAACD,GAAG,EAAEN,IAAI,CAACM,GAAG,CAAC,CAAC;EAC7C;AACJ;AAEA,SAAST,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}