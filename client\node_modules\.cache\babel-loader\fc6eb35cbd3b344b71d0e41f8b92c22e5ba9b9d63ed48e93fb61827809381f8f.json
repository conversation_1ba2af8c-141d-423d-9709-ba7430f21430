{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\pages\\\\Clients.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Helmet } from 'react-helmet';\nimport apiService from '../services/api';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Clients = () => {\n  _s();\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchClients();\n  }, []);\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.public.getClients();\n      setClients(response.data);\n      setError(null);\n    } catch (error) {\n      console.error('Error fetching clients:', error);\n      setError('Failed to load clients');\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pt-20 min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        size: \"xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pt-20\",\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Corporate Clients - Al-Fayasel Drugstore\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Our valued corporate clients and partnerships in the pharmaceutical industry.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"bg-gradient-to-r from-primary-green to-primary-green-dark text-white py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"text-4xl lg:text-5xl font-bold mb-4\",\n          children: \"Corporate Clients\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.2\n          },\n          className: \"text-xl text-secondary-green\",\n          children: \"Trusted partnerships with leading healthcare organizations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 lg:py-24\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: error ? /*#__PURE__*/_jsxDEV(ErrorMessage, {\n          message: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this) : clients.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-8\",\n          children: clients.map((client, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.05\n            },\n            viewport: {\n              once: true\n            },\n            className: \"card text-center group\",\n            children: [client.logo_url ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: client.logo_url,\n              alt: client.name,\n              className: \"w-full h-20 object-contain mb-4 group-hover:scale-110 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full h-20 bg-gray-100 rounded-lg flex items-center justify-center mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-400 font-medium\",\n                children: client.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-800\",\n              children: client.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 19\n            }, this), client.partnership_year && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 mt-1\",\n              children: [\"Partner since \", client.partnership_year]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 21\n            }, this)]\n          }, client.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 text-lg\",\n            children: \"No clients to display at the moment.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n_s(Clients, \"zQV1hJCcmbjAPczvHNExEVBWQBA=\");\n_c = Clients;\nexport default Clients;\nvar _c;\n$RefreshReg$(_c, \"Clients\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "<PERSON><PERSON><PERSON>", "apiService", "LoadingSpinner", "ErrorMessage", "jsxDEV", "_jsxDEV", "Clients", "_s", "clients", "setClients", "loading", "setLoading", "error", "setError", "fetchClients", "response", "public", "getClients", "data", "console", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "h1", "initial", "opacity", "y", "animate", "transition", "duration", "p", "delay", "message", "length", "map", "client", "index", "div", "scale", "whileInView", "viewport", "once", "logo_url", "src", "alt", "partnership_year", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/pages/Clients.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Helmet } from 'react-helmet';\nimport apiService from '../services/api';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\n\nconst Clients = () => {\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchClients();\n  }, []);\n\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.public.getClients();\n      setClients(response.data);\n      setError(null);\n    } catch (error) {\n      console.error('Error fetching clients:', error);\n      setError('Failed to load clients');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"pt-20 min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"xl\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"pt-20\">\n      <Helmet>\n        <title>Corporate Clients - Al-Fayasel Drugstore</title>\n        <meta name=\"description\" content=\"Our valued corporate clients and partnerships in the pharmaceutical industry.\" />\n      </Helmet>\n\n      {/* Page Header */}\n      <section className=\"bg-gradient-to-r from-primary-green to-primary-green-dark text-white py-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <motion.h1\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-4xl lg:text-5xl font-bold mb-4\"\n          >\n            Corporate Clients\n          </motion.h1>\n          <motion.p\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-xl text-secondary-green\"\n          >\n            Trusted partnerships with leading healthcare organizations\n          </motion.p>\n        </div>\n      </section>\n\n      {/* Clients Grid */}\n      <section className=\"py-16 lg:py-24\">\n        <div className=\"container mx-auto px-4\">\n          {error ? (\n            <ErrorMessage message={error} />\n          ) : clients.length > 0 ? (\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-8\">\n              {clients.map((client, index) => (\n                <motion.div\n                  key={client.id}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: index * 0.05 }}\n                  viewport={{ once: true }}\n                  className=\"card text-center group\"\n                >\n                  {client.logo_url ? (\n                    <img\n                      src={client.logo_url}\n                      alt={client.name}\n                      className=\"w-full h-20 object-contain mb-4 group-hover:scale-110 transition-transform duration-300\"\n                    />\n                  ) : (\n                    <div className=\"w-full h-20 bg-gray-100 rounded-lg flex items-center justify-center mb-4\">\n                      <span className=\"text-gray-400 font-medium\">{client.name}</span>\n                    </div>\n                  )}\n                  \n                  <h3 className=\"text-sm font-medium text-gray-800\">\n                    {client.name}\n                  </h3>\n                  \n                  {client.partnership_year && (\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      Partner since {client.partnership_year}\n                    </p>\n                  )}\n                </motion.div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <p className=\"text-gray-600 text-lg\">No clients to display at the moment.</p>\n            </div>\n          )}\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Clients;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,QAAQ,cAAc;AACrC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdgB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMd,UAAU,CAACe,MAAM,CAACC,UAAU,CAAC,CAAC;MACrDR,UAAU,CAACM,QAAQ,CAACG,IAAI,CAAC;MACzBL,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,wBAAwB,CAAC;IACpC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKe,SAAS,EAAC,qDAAqD;MAAAC,QAAA,eAClEhB,OAAA,CAACH,cAAc;QAACoB,IAAI,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAEV;EAEA,oBACErB,OAAA;IAAKe,SAAS,EAAC,OAAO;IAAAC,QAAA,gBACpBhB,OAAA,CAACL,MAAM;MAAAqB,QAAA,gBACLhB,OAAA;QAAAgB,QAAA,EAAO;MAAwC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACvDrB,OAAA;QAAMsB,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAA+E;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7G,CAAC,eAGTrB,OAAA;MAASe,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eAC7FhB,OAAA;QAAKe,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDhB,OAAA,CAACN,MAAM,CAAC8B,EAAE;UACRC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9Bf,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAChD;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZrB,OAAA,CAACN,MAAM,CAACqC,CAAC;UACPN,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEE,KAAK,EAAE;UAAI,CAAE;UAC1CjB,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EACzC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrB,OAAA;MAASe,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjChB,OAAA;QAAKe,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EACpCT,KAAK,gBACJP,OAAA,CAACF,YAAY;UAACmC,OAAO,EAAE1B;QAAM;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAC9BlB,OAAO,CAAC+B,MAAM,GAAG,CAAC,gBACpBlC,OAAA;UAAKe,SAAS,EAAC,qEAAqE;UAAAC,QAAA,EACjFb,OAAO,CAACgC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBrC,OAAA,CAACN,MAAM,CAAC4C,GAAG;YAETb,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEa,KAAK,EAAE;YAAI,CAAE;YACpCC,WAAW,EAAE;cAAEd,OAAO,EAAE,CAAC;cAAEa,KAAK,EAAE;YAAE,CAAE;YACtCV,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEE,KAAK,EAAEK,KAAK,GAAG;YAAK,CAAE;YACnDI,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzB3B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,GAEjCoB,MAAM,CAACO,QAAQ,gBACd3C,OAAA;cACE4C,GAAG,EAAER,MAAM,CAACO,QAAS;cACrBE,GAAG,EAAET,MAAM,CAACd,IAAK;cACjBP,SAAS,EAAC;YAAyF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpG,CAAC,gBAEFrB,OAAA;cAAKe,SAAS,EAAC,0EAA0E;cAAAC,QAAA,eACvFhB,OAAA;gBAAMe,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEoB,MAAM,CAACd;cAAI;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CACN,eAEDrB,OAAA;cAAIe,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAC9CoB,MAAM,CAACd;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAEJe,MAAM,CAACU,gBAAgB,iBACtB9C,OAAA;cAAGe,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,gBAC1B,EAACoB,MAAM,CAACU,gBAAgB;YAAA;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CACJ;UAAA,GA3BIe,MAAM,CAACW,EAAE;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4BJ,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENrB,OAAA;UAAKe,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChChB,OAAA;YAAGe,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnB,EAAA,CA7GID,OAAO;AAAA+C,EAAA,GAAP/C,OAAO;AA+Gb,eAAeA,OAAO;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}