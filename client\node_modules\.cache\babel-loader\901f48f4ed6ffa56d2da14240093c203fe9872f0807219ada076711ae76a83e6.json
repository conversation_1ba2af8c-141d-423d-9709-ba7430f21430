{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n  const checkAuthStatus = async () => {\n    try {\n      const token = localStorage.getItem('authToken');\n      const savedUser = localStorage.getItem('user');\n      if (token && savedUser) {\n        const response = await apiService.auth.getMe();\n        setUser(response.data.user);\n        setIsAuthenticated(true);\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      logout();\n    } finally {\n      setLoading(false);\n    }\n  };\n  const login = async credentials => {\n    try {\n      const response = await apiService.auth.login(credentials);\n      const {\n        user,\n        token\n      } = response.data;\n      localStorage.setItem('authToken', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      setUser(user);\n      setIsAuthenticated(true);\n      return {\n        success: true,\n        user\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Login failed';\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      await apiService.auth.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('authToken');\n      localStorage.removeItem('user');\n      setUser(null);\n      setIsAuthenticated(false);\n    }\n  };\n  const changePassword = async passwordData => {\n    try {\n      await apiService.auth.changePassword(passwordData);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || 'Failed to change password';\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const value = {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    changePassword,\n    checkAuthStatus\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"xBgiRagNfQVCfEr2dT2PptfN+TE=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "apiService", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "isAuthenticated", "setIsAuthenticated", "checkAuthStatus", "token", "localStorage", "getItem", "savedUser", "response", "auth", "getMe", "data", "error", "console", "logout", "login", "credentials", "setItem", "JSON", "stringify", "success", "_error$response", "_error$response$data", "message", "removeItem", "changePassword", "passwordData", "_error$response2", "_error$response2$data", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport apiService from '../services/api';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n\n  const checkAuthStatus = async () => {\n    try {\n      const token = localStorage.getItem('authToken');\n      const savedUser = localStorage.getItem('user');\n      \n      if (token && savedUser) {\n        const response = await apiService.auth.getMe();\n        setUser(response.data.user);\n        setIsAuthenticated(true);\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      logout();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = async (credentials) => {\n    try {\n      const response = await apiService.auth.login(credentials);\n      const { user, token } = response.data;\n      \n      localStorage.setItem('authToken', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      \n      setUser(user);\n      setIsAuthenticated(true);\n      \n      return { success: true, user };\n    } catch (error) {\n      const message = error.response?.data?.error || 'Login failed';\n      return { success: false, error: message };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await apiService.auth.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('authToken');\n      localStorage.removeItem('user');\n      setUser(null);\n      setIsAuthenticated(false);\n    }\n  };\n\n  const changePassword = async (passwordData) => {\n    try {\n      await apiService.auth.changePassword(passwordData);\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.error || 'Failed to change password';\n      return { success: false, error: message };\n    }\n  };\n\n  const value = {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    changePassword,\n    checkAuthStatus\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACdkB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAC/C,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE9C,IAAIF,KAAK,IAAIG,SAAS,EAAE;QACtB,MAAMC,QAAQ,GAAG,MAAMtB,UAAU,CAACuB,IAAI,CAACC,KAAK,CAAC,CAAC;QAC9CZ,OAAO,CAACU,QAAQ,CAACG,IAAI,CAACd,IAAI,CAAC;QAC3BK,kBAAkB,CAAC,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CE,MAAM,CAAC,CAAC;IACV,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMe,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMtB,UAAU,CAACuB,IAAI,CAACM,KAAK,CAACC,WAAW,CAAC;MACzD,MAAM;QAAEnB,IAAI;QAAEO;MAAM,CAAC,GAAGI,QAAQ,CAACG,IAAI;MAErCN,YAAY,CAACY,OAAO,CAAC,WAAW,EAAEb,KAAK,CAAC;MACxCC,YAAY,CAACY,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACtB,IAAI,CAAC,CAAC;MAElDC,OAAO,CAACD,IAAI,CAAC;MACbK,kBAAkB,CAAC,IAAI,CAAC;MAExB,OAAO;QAAEkB,OAAO,EAAE,IAAI;QAAEvB;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOe,KAAK,EAAE;MAAA,IAAAS,eAAA,EAAAC,oBAAA;MACd,MAAMC,OAAO,GAAG,EAAAF,eAAA,GAAAT,KAAK,CAACJ,QAAQ,cAAAa,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBV,KAAK,KAAI,cAAc;MAC7D,OAAO;QAAEQ,OAAO,EAAE,KAAK;QAAER,KAAK,EAAEW;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMT,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAM5B,UAAU,CAACuB,IAAI,CAACK,MAAM,CAAC,CAAC;IAChC,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRP,YAAY,CAACmB,UAAU,CAAC,WAAW,CAAC;MACpCnB,YAAY,CAACmB,UAAU,CAAC,MAAM,CAAC;MAC/B1B,OAAO,CAAC,IAAI,CAAC;MACbI,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMuB,cAAc,GAAG,MAAOC,YAAY,IAAK;IAC7C,IAAI;MACF,MAAMxC,UAAU,CAACuB,IAAI,CAACgB,cAAc,CAACC,YAAY,CAAC;MAClD,OAAO;QAAEN,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAe,gBAAA,EAAAC,qBAAA;MACd,MAAML,OAAO,GAAG,EAAAI,gBAAA,GAAAf,KAAK,CAACJ,QAAQ,cAAAmB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBhB,KAAK,KAAI,2BAA2B;MAC1E,OAAO;QAAEQ,OAAO,EAAE,KAAK;QAAER,KAAK,EAAEW;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMM,KAAK,GAAG;IACZhC,IAAI;IACJI,eAAe;IACfF,OAAO;IACPgB,KAAK;IACLD,MAAM;IACNW,cAAc;IACdtB;EACF,CAAC;EAED,oBACEf,OAAA,CAACC,WAAW,CAACyC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAlC,QAAA,EAChCA;EAAQ;IAAAoC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACtC,GAAA,CApFWF,YAAY;AAAAyC,EAAA,GAAZzC,YAAY;AAAA,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}