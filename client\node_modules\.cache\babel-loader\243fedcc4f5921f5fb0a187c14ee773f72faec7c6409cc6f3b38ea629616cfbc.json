{"ast": null, "code": "function createLock(name) {\n  let lock = null;\n  return () => {\n    const openLock = () => {\n      lock = null;\n    };\n    if (lock === null) {\n      lock = name;\n      return openLock;\n    }\n    return false;\n  };\n}\nconst globalHorizontalLock = createLock(\"dragHorizontal\");\nconst globalVerticalLock = createLock(\"dragVertical\");\nfunction getGlobalLock(drag) {\n  let lock = false;\n  if (drag === \"y\") {\n    lock = globalVerticalLock();\n  } else if (drag === \"x\") {\n    lock = globalHorizontalLock();\n  } else {\n    const openHorizontal = globalHorizontalLock();\n    const openVertical = globalVerticalLock();\n    if (openHorizontal && openVertical) {\n      lock = () => {\n        openHorizontal();\n        openVertical();\n      };\n    } else {\n      // Release the locks because we don't use them\n      if (openHorizontal) openHorizontal();\n      if (openVertical) openVertical();\n    }\n  }\n  return lock;\n}\nfunction isDragActive() {\n  // Check the gesture lock - if we get it, it means no drag gesture is active\n  // and we can safely fire the tap gesture.\n  const openGestureLock = getGlobalLock(true);\n  if (!openGestureLock) return true;\n  openGestureLock();\n  return false;\n}\nexport { createLock, getGlobalLock, isDragActive };", "map": {"version": 3, "names": ["createLock", "name", "lock", "openLock", "globalHorizontalLock", "globalVerticalLock", "getGlobalLock", "drag", "openHorizontal", "openVertical", "isDragActive", "openGestureLock"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/node_modules/framer-motion/dist/es/gestures/drag/utils/lock.mjs"], "sourcesContent": ["function createLock(name) {\n    let lock = null;\n    return () => {\n        const openLock = () => {\n            lock = null;\n        };\n        if (lock === null) {\n            lock = name;\n            return openLock;\n        }\n        return false;\n    };\n}\nconst globalHorizontalLock = createLock(\"dragHorizontal\");\nconst globalVerticalLock = createLock(\"dragVertical\");\nfunction getGlobalLock(drag) {\n    let lock = false;\n    if (drag === \"y\") {\n        lock = globalVerticalLock();\n    }\n    else if (drag === \"x\") {\n        lock = globalHorizontalLock();\n    }\n    else {\n        const openHorizontal = globalHorizontalLock();\n        const openVertical = globalVerticalLock();\n        if (openHorizontal && openVertical) {\n            lock = () => {\n                openHorizontal();\n                openVertical();\n            };\n        }\n        else {\n            // Release the locks because we don't use them\n            if (openHorizontal)\n                openHorizontal();\n            if (openVertical)\n                openVertical();\n        }\n    }\n    return lock;\n}\nfunction isDragActive() {\n    // Check the gesture lock - if we get it, it means no drag gesture is active\n    // and we can safely fire the tap gesture.\n    const openGestureLock = getGlobalLock(true);\n    if (!openGestureLock)\n        return true;\n    openGestureLock();\n    return false;\n}\n\nexport { createLock, getGlobalLock, isDragActive };\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,IAAI,EAAE;EACtB,IAAIC,IAAI,GAAG,IAAI;EACf,OAAO,MAAM;IACT,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACnBD,IAAI,GAAG,IAAI;IACf,CAAC;IACD,IAAIA,IAAI,KAAK,IAAI,EAAE;MACfA,IAAI,GAAGD,IAAI;MACX,OAAOE,QAAQ;IACnB;IACA,OAAO,KAAK;EAChB,CAAC;AACL;AACA,MAAMC,oBAAoB,GAAGJ,UAAU,CAAC,gBAAgB,CAAC;AACzD,MAAMK,kBAAkB,GAAGL,UAAU,CAAC,cAAc,CAAC;AACrD,SAASM,aAAaA,CAACC,IAAI,EAAE;EACzB,IAAIL,IAAI,GAAG,KAAK;EAChB,IAAIK,IAAI,KAAK,GAAG,EAAE;IACdL,IAAI,GAAGG,kBAAkB,CAAC,CAAC;EAC/B,CAAC,MACI,IAAIE,IAAI,KAAK,GAAG,EAAE;IACnBL,IAAI,GAAGE,oBAAoB,CAAC,CAAC;EACjC,CAAC,MACI;IACD,MAAMI,cAAc,GAAGJ,oBAAoB,CAAC,CAAC;IAC7C,MAAMK,YAAY,GAAGJ,kBAAkB,CAAC,CAAC;IACzC,IAAIG,cAAc,IAAIC,YAAY,EAAE;MAChCP,IAAI,GAAGA,CAAA,KAAM;QACTM,cAAc,CAAC,CAAC;QAChBC,YAAY,CAAC,CAAC;MAClB,CAAC;IACL,CAAC,MACI;MACD;MACA,IAAID,cAAc,EACdA,cAAc,CAAC,CAAC;MACpB,IAAIC,YAAY,EACZA,YAAY,CAAC,CAAC;IACtB;EACJ;EACA,OAAOP,IAAI;AACf;AACA,SAASQ,YAAYA,CAAA,EAAG;EACpB;EACA;EACA,MAAMC,eAAe,GAAGL,aAAa,CAAC,IAAI,CAAC;EAC3C,IAAI,CAACK,eAAe,EAChB,OAAO,IAAI;EACfA,eAAe,CAAC,CAAC;EACjB,OAAO,KAAK;AAChB;AAEA,SAASX,UAAU,EAAEM,aAAa,EAAEI,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}