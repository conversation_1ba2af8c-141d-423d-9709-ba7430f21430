const JsonDatabase = require('./JsonDatabase');

class BaseModel {
  constructor(tableName) {
    this.tableName = tableName;
    this.db = new JsonDatabase();
  }

  async connect() {
    await this.db.connect();
  }

  async findAll(orderBy = 'order_index ASC, created_at DESC') {
    await this.connect();
    return this.db.findAll(this.tableName, orderBy);
  }

  async findById(id) {
    await this.connect();
    return this.db.findById(this.tableName, id);
  }

  async findByField(field, value) {
    await this.connect();
    return this.db.findByField(this.tableName, field, value);
  }

  async create(data) {
    await this.connect();
    return this.db.create(this.tableName, data);
  }

  async update(id, data) {
    await this.connect();
    return this.db.update(this.tableName, id, data);
  }

  async delete(id) {
    await this.connect();
    return this.db.delete(this.tableName, id);
  }

  async count() {
    await this.connect();
    return this.db.count(this.tableName);
  }

  async search(searchTerm, searchFields = ['name']) {
    await this.connect();
    const whereClause = searchFields.map(field => `${field} LIKE ?`).join(' OR ');
    const searchValue = `%${searchTerm}%`;
    const params = searchFields.map(() => searchValue);
    
    return await this.db.all(
      `SELECT * FROM ${this.tableName} WHERE ${whereClause} ORDER BY order_index ASC, created_at DESC`,
      params
    );
  }
}

module.exports = BaseModel;
