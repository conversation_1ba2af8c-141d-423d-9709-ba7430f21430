{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Al-Faiasel\\\\client\\\\src\\\\admin\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { FaBoxes, FaTruck, FaUsers, FaNewspaper, FaPlus, FaEye, FaEdit, FaChartLine } from 'react-icons/fa';\nimport apiService from '../services/api';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const [stats, setStats] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchDashboardStats();\n  }, []);\n  const fetchDashboardStats = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.admin.getDashboardStats();\n      setStats(response.data);\n      setError(null);\n    } catch (error) {\n      console.error('Error fetching dashboard stats:', error);\n      setError('Failed to load dashboard statistics');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const statCards = [{\n    title: 'Products',\n    value: stats.products || 0,\n    icon: FaBoxes,\n    color: 'bg-blue-500',\n    link: '/admin/products',\n    addLink: '/admin/products'\n  }, {\n    title: 'Suppliers',\n    value: stats.suppliers || 0,\n    icon: FaTruck,\n    color: 'bg-green-500',\n    link: '/admin/suppliers',\n    addLink: '/admin/suppliers'\n  }, {\n    title: 'Corporate Clients',\n    value: stats.clients || 0,\n    icon: FaUsers,\n    color: 'bg-purple-500',\n    link: '/admin/clients',\n    addLink: '/admin/clients'\n  }, {\n    title: 'News Articles',\n    value: stats.news || 0,\n    icon: FaNewspaper,\n    color: 'bg-orange-500',\n    link: '/admin/news',\n    addLink: '/admin/news'\n  }];\n  const quickActions = [{\n    title: 'Add New Product',\n    icon: FaBoxes,\n    link: '/admin/products',\n    color: 'bg-blue-500'\n  }, {\n    title: 'Add New Supplier',\n    icon: FaTruck,\n    link: '/admin/suppliers',\n    color: 'bg-green-500'\n  }, {\n    title: 'Add News Article',\n    icon: FaNewspaper,\n    link: '/admin/news',\n    color: 'bg-orange-500'\n  }, {\n    title: 'Manage Team',\n    icon: FaUsers,\n    link: '/admin/team',\n    color: 'bg-purple-500'\n  }, {\n    title: 'Site Settings',\n    icon: FaEdit,\n    link: '/admin/settings',\n    color: 'bg-gray-500'\n  }, {\n    title: 'View Website',\n    icon: FaEye,\n    link: '/',\n    color: 'bg-primary-green',\n    external: true\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center py-12\",\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        size: \"xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-800 mb-2\",\n        children: \"Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Welcome back! Here's an overview of your content.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n      message: error,\n      className: \"mb-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n      children: statCards.map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.5,\n          delay: index * 0.1\n        },\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600 mb-1\",\n              children: stat.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-gray-800\",\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`,\n            children: /*#__PURE__*/_jsxDEV(stat.icon, {\n              className: \"text-white text-xl\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mt-4 pt-4 border-t border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: stat.link,\n            className: \"text-primary-green hover:text-primary-green-dark text-sm font-medium transition-colors duration-200\",\n            children: \"View All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: stat.addLink,\n            className: \"text-gray-500 hover:text-primary-green transition-colors duration-200\",\n            children: /*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this)]\n      }, stat.title, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-800 mb-6\",\n        children: \"Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n        children: quickActions.map((action, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.95\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.3,\n            delay: index * 0.05\n          },\n          children: action.external ? /*#__PURE__*/_jsxDEV(\"a\", {\n            href: action.link,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-primary-green hover:bg-primary-green/5 transition-all duration-200 group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-10 h-10 ${action.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200`,\n              children: /*#__PURE__*/_jsxDEV(action.icon, {\n                className: \"text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-gray-700 group-hover:text-primary-green\",\n              children: action.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Link, {\n            to: action.link,\n            className: \"flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-primary-green hover:bg-primary-green/5 transition-all duration-200 group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-10 h-10 ${action.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200`,\n              children: /*#__PURE__*/_jsxDEV(action.icon, {\n                className: \"text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-gray-700 group-hover:text-primary-green\",\n              children: action.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 17\n          }, this)\n        }, action.title, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-800 mb-6\",\n        children: \"Content Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-700 mb-4\",\n            children: \"Website Content\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/admin/about\",\n                className: \"text-primary-green hover:text-primary-green-dark\",\n                children: \"Manage About Us content and team profiles\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/admin/services\",\n                className: \"text-primary-green hover:text-primary-green-dark\",\n                children: \"Update services and offerings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/admin/contact\",\n                className: \"text-primary-green hover:text-primary-green-dark\",\n                children: \"Edit contact information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-700 mb-4\",\n            children: \"Business Data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/admin/products\",\n                className: \"text-primary-green hover:text-primary-green-dark\",\n                children: \"Manage product catalog\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/admin/suppliers\",\n                className: \"text-primary-green hover:text-primary-green-dark\",\n                children: \"Update supplier information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/admin/clients\",\n                className: \"text-primary-green hover:text-primary-green-dark\",\n                children: \"Manage corporate client list\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"h9VXH4/xnngUJwEgxQ01Gk81HxU=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "motion", "FaBoxes", "FaTruck", "FaUsers", "FaNewspaper", "FaPlus", "FaEye", "FaEdit", "FaChartLine", "apiService", "LoadingSpinner", "ErrorMessage", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "stats", "setStats", "loading", "setLoading", "error", "setError", "fetchDashboardStats", "response", "admin", "getDashboardStats", "data", "console", "statCards", "title", "value", "products", "icon", "color", "link", "addLink", "suppliers", "clients", "news", "quickActions", "external", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "map", "stat", "index", "div", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "to", "action", "scale", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/src/admin/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { \n  FaBoxes, \n  FaTruck, \n  FaUsers, \n  FaNewspaper, \n  FaPlus, \n  FaEye,\n  FaEdit,\n  FaChartLine\n} from 'react-icons/fa';\nimport apiService from '../services/api';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\n\nconst AdminDashboard = () => {\n  const [stats, setStats] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchDashboardStats();\n  }, []);\n\n  const fetchDashboardStats = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.admin.getDashboardStats();\n      setStats(response.data);\n      setError(null);\n    } catch (error) {\n      console.error('Error fetching dashboard stats:', error);\n      setError('Failed to load dashboard statistics');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const statCards = [\n    {\n      title: 'Products',\n      value: stats.products || 0,\n      icon: FaBoxes,\n      color: 'bg-blue-500',\n      link: '/admin/products',\n      addLink: '/admin/products'\n    },\n    {\n      title: 'Suppliers',\n      value: stats.suppliers || 0,\n      icon: FaTruck,\n      color: 'bg-green-500',\n      link: '/admin/suppliers',\n      addLink: '/admin/suppliers'\n    },\n    {\n      title: 'Corporate Clients',\n      value: stats.clients || 0,\n      icon: FaUsers,\n      color: 'bg-purple-500',\n      link: '/admin/clients',\n      addLink: '/admin/clients'\n    },\n    {\n      title: 'News Articles',\n      value: stats.news || 0,\n      icon: FaNewspaper,\n      color: 'bg-orange-500',\n      link: '/admin/news',\n      addLink: '/admin/news'\n    }\n  ];\n\n  const quickActions = [\n    { title: 'Add New Product', icon: FaBoxes, link: '/admin/products', color: 'bg-blue-500' },\n    { title: 'Add New Supplier', icon: FaTruck, link: '/admin/suppliers', color: 'bg-green-500' },\n    { title: 'Add News Article', icon: FaNewspaper, link: '/admin/news', color: 'bg-orange-500' },\n    { title: 'Manage Team', icon: FaUsers, link: '/admin/team', color: 'bg-purple-500' },\n    { title: 'Site Settings', icon: FaEdit, link: '/admin/settings', color: 'bg-gray-500' },\n    { title: 'View Website', icon: FaEye, link: '/', color: 'bg-primary-green', external: true }\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-12\">\n        <LoadingSpinner size=\"xl\" />\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* Page Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">Dashboard</h1>\n        <p className=\"text-gray-600\">Welcome back! Here's an overview of your content.</p>\n      </div>\n\n      {error && <ErrorMessage message={error} className=\"mb-6\" />}\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        {statCards.map((stat, index) => (\n          <motion.div\n            key={stat.title}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: index * 0.1 }}\n            className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\"\n          >\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600 mb-1\">{stat.title}</p>\n                <p className=\"text-3xl font-bold text-gray-800\">{stat.value}</p>\n              </div>\n              <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>\n                <stat.icon className=\"text-white text-xl\" />\n              </div>\n            </div>\n            \n            <div className=\"flex items-center justify-between mt-4 pt-4 border-t border-gray-100\">\n              <Link\n                to={stat.link}\n                className=\"text-primary-green hover:text-primary-green-dark text-sm font-medium transition-colors duration-200\"\n              >\n                View All\n              </Link>\n              <Link\n                to={stat.addLink}\n                className=\"text-gray-500 hover:text-primary-green transition-colors duration-200\"\n              >\n                <FaPlus />\n              </Link>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n        <h2 className=\"text-xl font-semibold text-gray-800 mb-6\">Quick Actions</h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {quickActions.map((action, index) => (\n            <motion.div\n              key={action.title}\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.3, delay: index * 0.05 }}\n            >\n              {action.external ? (\n                <a\n                  href={action.link}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-primary-green hover:bg-primary-green/5 transition-all duration-200 group\"\n                >\n                  <div className={`w-10 h-10 ${action.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}>\n                    <action.icon className=\"text-white\" />\n                  </div>\n                  <span className=\"font-medium text-gray-700 group-hover:text-primary-green\">\n                    {action.title}\n                  </span>\n                </a>\n              ) : (\n                <Link\n                  to={action.link}\n                  className=\"flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-primary-green hover:bg-primary-green/5 transition-all duration-200 group\"\n                >\n                  <div className={`w-10 h-10 ${action.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}>\n                    <action.icon className=\"text-white\" />\n                  </div>\n                  <span className=\"font-medium text-gray-700 group-hover:text-primary-green\">\n                    {action.title}\n                  </span>\n                </Link>\n              )}\n            </motion.div>\n          ))}\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h2 className=\"text-xl font-semibold text-gray-800 mb-6\">Content Management</h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-700 mb-4\">Website Content</h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link to=\"/admin/about\" className=\"text-primary-green hover:text-primary-green-dark\">\n                  Manage About Us content and team profiles\n                </Link>\n              </li>\n              <li>\n                <Link to=\"/admin/services\" className=\"text-primary-green hover:text-primary-green-dark\">\n                  Update services and offerings\n                </Link>\n              </li>\n              <li>\n                <Link to=\"/admin/contact\" className=\"text-primary-green hover:text-primary-green-dark\">\n                  Edit contact information\n                </Link>\n              </li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-medium text-gray-700 mb-4\">Business Data</h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link to=\"/admin/products\" className=\"text-primary-green hover:text-primary-green-dark\">\n                  Manage product catalog\n                </Link>\n              </li>\n              <li>\n                <Link to=\"/admin/suppliers\" className=\"text-primary-green hover:text-primary-green-dark\">\n                  Update supplier information\n                </Link>\n              </li>\n              <li>\n                <Link to=\"/admin/clients\" className=\"text-primary-green hover:text-primary-green-dark\">\n                  Manage corporate client list\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,WAAW,EACXC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,WAAW,QACN,gBAAgB;AACvB,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,YAAY,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdwB,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMd,UAAU,CAACe,KAAK,CAACC,iBAAiB,CAAC,CAAC;MAC3DR,QAAQ,CAACM,QAAQ,CAACG,IAAI,CAAC;MACvBL,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDC,QAAQ,CAAC,qCAAqC,CAAC;IACjD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,SAAS,GAAG,CAChB;IACEC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAEd,KAAK,CAACe,QAAQ,IAAI,CAAC;IAC1BC,IAAI,EAAE/B,OAAO;IACbgC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE;EACX,CAAC,EACD;IACEN,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAEd,KAAK,CAACoB,SAAS,IAAI,CAAC;IAC3BJ,IAAI,EAAE9B,OAAO;IACb+B,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE,kBAAkB;IACxBC,OAAO,EAAE;EACX,CAAC,EACD;IACEN,KAAK,EAAE,mBAAmB;IAC1BC,KAAK,EAAEd,KAAK,CAACqB,OAAO,IAAI,CAAC;IACzBL,IAAI,EAAE7B,OAAO;IACb8B,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE;EACX,CAAC,EACD;IACEN,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAEd,KAAK,CAACsB,IAAI,IAAI,CAAC;IACtBN,IAAI,EAAE5B,WAAW;IACjB6B,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE;EACX,CAAC,CACF;EAED,MAAMI,YAAY,GAAG,CACnB;IAAEV,KAAK,EAAE,iBAAiB;IAAEG,IAAI,EAAE/B,OAAO;IAAEiC,IAAI,EAAE,iBAAiB;IAAED,KAAK,EAAE;EAAc,CAAC,EAC1F;IAAEJ,KAAK,EAAE,kBAAkB;IAAEG,IAAI,EAAE9B,OAAO;IAAEgC,IAAI,EAAE,kBAAkB;IAAED,KAAK,EAAE;EAAe,CAAC,EAC7F;IAAEJ,KAAK,EAAE,kBAAkB;IAAEG,IAAI,EAAE5B,WAAW;IAAE8B,IAAI,EAAE,aAAa;IAAED,KAAK,EAAE;EAAgB,CAAC,EAC7F;IAAEJ,KAAK,EAAE,aAAa;IAAEG,IAAI,EAAE7B,OAAO;IAAE+B,IAAI,EAAE,aAAa;IAAED,KAAK,EAAE;EAAgB,CAAC,EACpF;IAAEJ,KAAK,EAAE,eAAe;IAAEG,IAAI,EAAEzB,MAAM;IAAE2B,IAAI,EAAE,iBAAiB;IAAED,KAAK,EAAE;EAAc,CAAC,EACvF;IAAEJ,KAAK,EAAE,cAAc;IAAEG,IAAI,EAAE1B,KAAK;IAAE4B,IAAI,EAAE,GAAG;IAAED,KAAK,EAAE,kBAAkB;IAAEO,QAAQ,EAAE;EAAK,CAAC,CAC7F;EAED,IAAItB,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK4B,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrD7B,OAAA,CAACH,cAAc;QAACiC,IAAI,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAEV;EAEA,oBACElC,OAAA;IAAA6B,QAAA,gBAEE7B,OAAA;MAAK4B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB7B,OAAA;QAAI4B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpElC,OAAA;QAAG4B,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAiD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC,EAEL3B,KAAK,iBAAIP,OAAA,CAACF,YAAY;MAACqC,OAAO,EAAE5B,KAAM;MAACqB,SAAS,EAAC;IAAM;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG3DlC,OAAA;MAAK4B,SAAS,EAAC,2DAA2D;MAAAC,QAAA,EACvEd,SAAS,CAACqB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBtC,OAAA,CAACb,MAAM,CAACoD,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,KAAK,EAAER,KAAK,GAAG;QAAI,CAAE;QAClDV,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBAEpE7B,OAAA;UAAK4B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD7B,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAG4B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAEQ,IAAI,CAACrB;YAAK;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtElC,OAAA;cAAG4B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEQ,IAAI,CAACpB;YAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACNlC,OAAA;YAAK4B,SAAS,EAAE,aAAaS,IAAI,CAACjB,KAAK,8CAA+C;YAAAS,QAAA,eACpF7B,OAAA,CAACqC,IAAI,CAAClB,IAAI;cAACS,SAAS,EAAC;YAAoB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlC,OAAA;UAAK4B,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnF7B,OAAA,CAACd,IAAI;YACH6D,EAAE,EAAEV,IAAI,CAAChB,IAAK;YACdO,SAAS,EAAC,qGAAqG;YAAAC,QAAA,EAChH;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPlC,OAAA,CAACd,IAAI;YACH6D,EAAE,EAAEV,IAAI,CAACf,OAAQ;YACjBM,SAAS,EAAC,uEAAuE;YAAAC,QAAA,eAEjF7B,OAAA,CAACR,MAAM;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA,GA7BDG,IAAI,CAACrB,KAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8BL,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNlC,OAAA;MAAK4B,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAC5E7B,OAAA;QAAI4B,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAa;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE3ElC,OAAA;QAAK4B,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEH,YAAY,CAACU,GAAG,CAAC,CAACY,MAAM,EAAEV,KAAK,kBAC9BtC,OAAA,CAACb,MAAM,CAACoD,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEQ,KAAK,EAAE;UAAK,CAAE;UACrCN,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEQ,KAAK,EAAE;UAAE,CAAE;UAClCL,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAER,KAAK,GAAG;UAAK,CAAE;UAAAT,QAAA,EAElDmB,MAAM,CAACrB,QAAQ,gBACd3B,OAAA;YACEkD,IAAI,EAAEF,MAAM,CAAC3B,IAAK;YAClB8B,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBxB,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,gBAEnK7B,OAAA;cAAK4B,SAAS,EAAE,aAAaoB,MAAM,CAAC5B,KAAK,sGAAuG;cAAAS,QAAA,eAC9I7B,OAAA,CAACgD,MAAM,CAAC7B,IAAI;gBAACS,SAAS,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACNlC,OAAA;cAAM4B,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EACvEmB,MAAM,CAAChC;YAAK;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAEJlC,OAAA,CAACd,IAAI;YACH6D,EAAE,EAAEC,MAAM,CAAC3B,IAAK;YAChBO,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,gBAEnK7B,OAAA;cAAK4B,SAAS,EAAE,aAAaoB,MAAM,CAAC5B,KAAK,sGAAuG;cAAAS,QAAA,eAC9I7B,OAAA,CAACgD,MAAM,CAAC7B,IAAI;gBAACS,SAAS,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACNlC,OAAA;cAAM4B,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EACvEmB,MAAM,CAAChC;YAAK;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACP,GA/BIc,MAAM,CAAChC,KAAK;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgCP,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlC,OAAA;MAAK4B,SAAS,EAAC,0DAA0D;MAAAC,QAAA,gBACvE7B,OAAA;QAAI4B,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAkB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEhFlC,OAAA;QAAK4B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD7B,OAAA;UAAA6B,QAAA,gBACE7B,OAAA;YAAI4B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3ElC,OAAA;YAAI4B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvB7B,OAAA;cAAA6B,QAAA,eACE7B,OAAA,CAACd,IAAI;gBAAC6D,EAAE,EAAC,cAAc;gBAACnB,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,EAAC;cAErF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLlC,OAAA;cAAA6B,QAAA,eACE7B,OAAA,CAACd,IAAI;gBAAC6D,EAAE,EAAC,iBAAiB;gBAACnB,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,EAAC;cAExF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLlC,OAAA;cAAA6B,QAAA,eACE7B,OAAA,CAACd,IAAI;gBAAC6D,EAAE,EAAC,gBAAgB;gBAACnB,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,EAAC;cAEvF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENlC,OAAA;UAAA6B,QAAA,gBACE7B,OAAA;YAAI4B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzElC,OAAA;YAAI4B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvB7B,OAAA;cAAA6B,QAAA,eACE7B,OAAA,CAACd,IAAI;gBAAC6D,EAAE,EAAC,iBAAiB;gBAACnB,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,EAAC;cAExF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLlC,OAAA;cAAA6B,QAAA,eACE7B,OAAA,CAACd,IAAI;gBAAC6D,EAAE,EAAC,kBAAkB;gBAACnB,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,EAAC;cAEzF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLlC,OAAA;cAAA6B,QAAA,eACE7B,OAAA,CAACd,IAAI;gBAAC6D,EAAE,EAAC,gBAAgB;gBAACnB,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,EAAC;cAEvF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChC,EAAA,CAzNID,cAAc;AAAAoD,EAAA,GAAdpD,cAAc;AA2NpB,eAAeA,cAAc;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}