{"ast": null, "code": "const clamp = (min, max, v) => Math.min(Math.max(v, min), max);\nexport { clamp };", "map": {"version": 3, "names": ["clamp", "min", "max", "v", "Math"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/node_modules/framer-motion/dist/es/utils/clamp.mjs"], "sourcesContent": ["const clamp = (min, max, v) => Math.min(Math.max(v, min), max);\n\nexport { clamp };\n"], "mappings": "AAAA,MAAMA,KAAK,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,CAAC,KAAKC,IAAI,CAACH,GAAG,CAACG,IAAI,CAACF,GAAG,CAACC,CAAC,EAAEF,GAAG,CAAC,EAAEC,GAAG,CAAC;AAE9D,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}