{"ast": null, "code": "import { scrollInfo } from './track.mjs';\nimport { observeTimeline } from './observe.mjs';\nimport { supportsScrollTimeline } from './supports.mjs';\nfunction scrollTimelineFallback({\n  source,\n  axis = \"y\"\n}) {\n  // ScrollTimeline records progress as a percentage CSSUnitValue\n  const currentTime = {\n    value: 0\n  };\n  const cancel = scrollInfo(info => {\n    currentTime.value = info[axis].progress * 100;\n  }, {\n    container: source,\n    axis\n  });\n  return {\n    currentTime,\n    cancel\n  };\n}\nconst timelineCache = new Map();\nfunction getTimeline({\n  source = document.documentElement,\n  axis = \"y\"\n} = {}) {\n  if (!timelineCache.has(source)) {\n    timelineCache.set(source, {});\n  }\n  const elementCache = timelineCache.get(source);\n  if (!elementCache[axis]) {\n    elementCache[axis] = supportsScrollTimeline() ? new ScrollTimeline({\n      source,\n      axis\n    }) : scrollTimelineFallback({\n      source,\n      axis\n    });\n  }\n  return elementCache[axis];\n}\nfunction scroll(onScroll, options) {\n  const timeline = getTimeline(options);\n  if (typeof onScroll === \"function\") {\n    return observeTimeline(onScroll, timeline);\n  } else {\n    return onScroll.attachTimeline(timeline);\n  }\n}\nexport { scroll };", "map": {"version": 3, "names": ["scrollInfo", "observeTimeline", "supportsScrollTimeline", "scrollTimelineFallback", "source", "axis", "currentTime", "value", "cancel", "info", "progress", "container", "timelineCache", "Map", "getTimeline", "document", "documentElement", "has", "set", "elementCache", "get", "ScrollTimeline", "scroll", "onScroll", "options", "timeline", "attachTimeline"], "sources": ["C:/Users/<USER>/Desktop/Al-Faiasel/client/node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs"], "sourcesContent": ["import { scrollInfo } from './track.mjs';\nimport { observeTimeline } from './observe.mjs';\nimport { supportsScrollTimeline } from './supports.mjs';\n\nfunction scrollTimelineFallback({ source, axis = \"y\" }) {\n    // ScrollTimeline records progress as a percentage CSSUnitValue\n    const currentTime = { value: 0 };\n    const cancel = scrollInfo((info) => {\n        currentTime.value = info[axis].progress * 100;\n    }, { container: source, axis });\n    return { currentTime, cancel };\n}\nconst timelineCache = new Map();\nfunction getTimeline({ source = document.documentElement, axis = \"y\", } = {}) {\n    if (!timelineCache.has(source)) {\n        timelineCache.set(source, {});\n    }\n    const elementCache = timelineCache.get(source);\n    if (!elementCache[axis]) {\n        elementCache[axis] = supportsScrollTimeline()\n            ? new ScrollTimeline({ source, axis })\n            : scrollTimelineFallback({ source, axis });\n    }\n    return elementCache[axis];\n}\nfunction scroll(onScroll, options) {\n    const timeline = getTimeline(options);\n    if (typeof onScroll === \"function\") {\n        return observeTimeline(onScroll, timeline);\n    }\n    else {\n        return onScroll.attachTimeline(timeline);\n    }\n}\n\nexport { scroll };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,aAAa;AACxC,SAASC,eAAe,QAAQ,eAAe;AAC/C,SAASC,sBAAsB,QAAQ,gBAAgB;AAEvD,SAASC,sBAAsBA,CAAC;EAAEC,MAAM;EAAEC,IAAI,GAAG;AAAI,CAAC,EAAE;EACpD;EACA,MAAMC,WAAW,GAAG;IAAEC,KAAK,EAAE;EAAE,CAAC;EAChC,MAAMC,MAAM,GAAGR,UAAU,CAAES,IAAI,IAAK;IAChCH,WAAW,CAACC,KAAK,GAAGE,IAAI,CAACJ,IAAI,CAAC,CAACK,QAAQ,GAAG,GAAG;EACjD,CAAC,EAAE;IAAEC,SAAS,EAAEP,MAAM;IAAEC;EAAK,CAAC,CAAC;EAC/B,OAAO;IAAEC,WAAW;IAAEE;EAAO,CAAC;AAClC;AACA,MAAMI,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC/B,SAASC,WAAWA,CAAC;EAAEV,MAAM,GAAGW,QAAQ,CAACC,eAAe;EAAEX,IAAI,GAAG;AAAK,CAAC,GAAG,CAAC,CAAC,EAAE;EAC1E,IAAI,CAACO,aAAa,CAACK,GAAG,CAACb,MAAM,CAAC,EAAE;IAC5BQ,aAAa,CAACM,GAAG,CAACd,MAAM,EAAE,CAAC,CAAC,CAAC;EACjC;EACA,MAAMe,YAAY,GAAGP,aAAa,CAACQ,GAAG,CAAChB,MAAM,CAAC;EAC9C,IAAI,CAACe,YAAY,CAACd,IAAI,CAAC,EAAE;IACrBc,YAAY,CAACd,IAAI,CAAC,GAAGH,sBAAsB,CAAC,CAAC,GACvC,IAAImB,cAAc,CAAC;MAAEjB,MAAM;MAAEC;IAAK,CAAC,CAAC,GACpCF,sBAAsB,CAAC;MAAEC,MAAM;MAAEC;IAAK,CAAC,CAAC;EAClD;EACA,OAAOc,YAAY,CAACd,IAAI,CAAC;AAC7B;AACA,SAASiB,MAAMA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC/B,MAAMC,QAAQ,GAAGX,WAAW,CAACU,OAAO,CAAC;EACrC,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;IAChC,OAAOtB,eAAe,CAACsB,QAAQ,EAAEE,QAAQ,CAAC;EAC9C,CAAC,MACI;IACD,OAAOF,QAAQ,CAACG,cAAc,CAACD,QAAQ,CAAC;EAC5C;AACJ;AAEA,SAASH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}